/**
 * 表单自动填充插件 - 弹窗脚本 (工作版本)
 */

console.log('表单填充插件开始加载...');

class FormFillerController {
    constructor() {
        this.currentTab = null;
        this.config = {
            template: 'default',
            brandScope: '测试',
            autoDropdown: true,
            autoDate: true,
            autoCheckbox: true
        };
        this.fieldCount = 0;
        
        this.init();
    }

    /**
     * 初始化弹窗控制器
     */
    async init() {
        try {
            console.log('开始初始化...');
            
            // 绑定事件监听器
            this.bindEvents();
            
            // 更新界面状态
            this.updateUI();
            
            // 尝试获取当前标签页
            try {
                await this.getCurrentTab();
                console.log('成功获取当前标签页:', this.currentTab?.url);
                
                // 自动预览表单字段
                await this.previewForm();
            } catch (error) {
                console.warn('获取标签页失败，但不影响基本功能:', error);
                this.showStatus('请打开一个网页后使用', 'warning');
            }
            
            console.log('初始化完成');
        } catch (error) {
            console.error('初始化失败:', error);
            this.showStatus('初始化失败', 'error');
        }
    }

    /**
     * 获取当前活动标签页
     */
    async getCurrentTab() {
        if (typeof chrome !== 'undefined' && chrome.tabs) {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            this.currentTab = tab;
            return tab;
        } else {
            throw new Error('Chrome API 不可用');
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        console.log('绑定事件监听器...');
        
        // 主要操作按钮
        const fillButton = document.getElementById('fill-form');
        const previewButton = document.getElementById('preview-form');
        const templateSelect = document.getElementById('template-select');
        const brandScopeInput = document.getElementById('brand-scope');
        
        if (fillButton) {
            fillButton.addEventListener('click', () => this.fillForm());
            console.log('一键填充按钮事件已绑定');
        }
        
        if (previewButton) {
            previewButton.addEventListener('click', () => this.previewForm());
            console.log('预览识别按钮事件已绑定');
        }
        
        if (templateSelect) {
            templateSelect.addEventListener('change', (e) => {
                this.config.template = e.target.value;
                this.applyTemplate(e.target.value);
                console.log('模板已切换到:', e.target.value);
            });
        }
        
        if (brandScopeInput) {
            brandScopeInput.addEventListener('input', (e) => {
                this.config.brandScope = e.target.value;
            });
        }
    }

    /**
     * 更新界面显示
     */
    updateUI() {
        const templateSelect = document.getElementById('template-select');
        const brandScopeInput = document.getElementById('brand-scope');
        
        if (templateSelect) {
            templateSelect.value = this.config.template;
        }
        
        if (brandScopeInput) {
            brandScopeInput.value = this.config.brandScope;
        }
    }

    /**
     * 应用模板配置
     */
    applyTemplate(templateName) {
        const templates = {
            'default': {
                brandScope: '测试'
            },
            'restaurant': {
                brandScope: '餐饮服务'
            },
            'retail': {
                brandScope: '零售商品'
            }
        };
        
        const template = templates[templateName];
        if (template) {
            this.config.brandScope = template.brandScope;
            this.updateUI();
            this.showStatus(`已应用${templateName}模板`, 'success');
        }
    }

    /**
     * 显示状态信息
     */
    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('status-text');
        if (statusElement) {
            statusElement.textContent = message;
            
            // 移除之前的状态类
            statusElement.classList.remove('status-success', 'status-error', 'status-warning');
            
            // 添加新的状态类
            if (type === 'success') {
                statusElement.classList.add('status-success');
            } else if (type === 'error') {
                statusElement.classList.add('status-error');
            } else if (type === 'warning') {
                statusElement.classList.add('status-warning');
            }
        }
        console.log(`状态更新: ${message} (${type})`);
    }

    /**
     * 更新字段计数显示
     */
    updateFieldCount(count) {
        this.fieldCount = count;
        const fieldCountElement = document.getElementById('field-count');
        if (fieldCountElement) {
            fieldCountElement.textContent = count;
        }
    }

    /**
     * 预览表单功能 - 这是识别按钮的功能
     */
    async previewForm() {
        try {
            this.showStatus('正在识别表单字段...', 'info');
            console.log('开始预览表单...');
            
            if (!this.currentTab) {
                this.showStatus('请先打开一个网页', 'warning');
                return;
            }
            
            // 发送消息到内容脚本
            const response = await this.sendMessageToContent({
                action: 'previewFields',
                config: this.config
            });
            
            if (response && response.success) {
                this.updateFieldCount(response.fieldCount);
                this.showStatus(`识别到 ${response.fieldCount} 个字段`, 'success');
                
                if (response.fieldCount > 0) {
                    console.log('识别到的字段详情:', response.fieldDetails);
                }
            } else {
                this.showStatus('未找到可填充字段', 'warning');
                this.updateFieldCount(0);
            }
        } catch (error) {
            console.error('预览表单失败:', error);
            this.showStatus('识别失败，请确保页面已完全加载', 'error');
            this.updateFieldCount(0);
        }
    }

    /**
     * 填充表单功能 - 这是一键填充按钮的功能
     */
    async fillForm() {
        try {
            console.log('开始填充表单...');
            
            if (!this.currentTab) {
                this.showStatus('请先打开一个网页', 'warning');
                return;
            }
            
            if (this.fieldCount === 0) {
                this.showStatus('请先点击"预览识别"', 'warning');
                return;
            }
            
            this.showStatus('正在填充表单...', 'info');
            
            const response = await this.sendMessageToContent({
                action: 'fillForm',
                config: this.config
            });
            
            if (response && response.success) {
                const successRate = ((response.filledCount / this.fieldCount) * 100).toFixed(1);
                this.showStatus(`成功填充 ${response.filledCount}/${this.fieldCount} 个字段 (${successRate}%)`, 'success');
                
                if (response.errors && response.errors.length > 0) {
                    console.warn('填充过程中的错误:', response.errors);
                }
            } else {
                this.showStatus('填充失败', 'error');
            }
        } catch (error) {
            console.error('填充表单失败:', error);
            this.showStatus('填充失败，请重试', 'error');
        }
    }

    /**
     * 向内容脚本发送消息
     */
    async sendMessageToContent(message) {
        try {
            if (!this.currentTab || !this.currentTab.id) {
                throw new Error('无效的标签页');
            }
            
            const response = await chrome.tabs.sendMessage(this.currentTab.id, message);
            return response;
        } catch (error) {
            console.error('发送消息失败:', error);
            
            // 如果是连接错误，可能是内容脚本未加载
            if (error.message.includes('Could not establish connection')) {
                throw new Error('无法连接到页面，请刷新页面后重试');
            }
            
            throw error;
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM加载完成，开始初始化插件...');
    new FormFillerController();
});

// 错误处理
window.addEventListener('error', (event) => {
    console.error('弹窗脚本错误:', event.error);
});

console.log('表单填充插件脚本加载完成');
