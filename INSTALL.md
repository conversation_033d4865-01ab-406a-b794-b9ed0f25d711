# 表单自动填充插件安装指南

## 🚀 快速安装

### 方法一：开发者模式安装（推荐）

1. **打开Chrome浏览器**
   - 确保使用Chrome浏览器版本88或更高版本

2. **进入扩展程序管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

3. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

4. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择插件文件夹：`f:\桌面\plugin`
   - 点击"选择文件夹"

5. **验证安装**
   - 插件图标应该出现在浏览器工具栏中
   - 如果没有看到图标，点击工具栏右侧的拼图图标，然后固定插件

### 方法二：打包安装

1. **打包插件**
   - 在扩展程序管理页面，点击"打包扩展程序"
   - 选择插件根目录：`f:\桌面\plugin`
   - 点击"打包扩展程序"按钮
   - 系统会生成 `.crx` 文件

2. **安装打包文件**
   - 将生成的 `.crx` 文件拖拽到扩展程序管理页面
   - 点击"添加扩展程序"确认安装

## 🎯 使用方法

### 基本使用

1. **打开测试页面**
   - 在浏览器中打开：`f:\桌面\plugin\test.html`
   - 或访问任何包含表单的网页

2. **启动插件**
   - 点击浏览器工具栏中的插件图标
   - 插件弹窗将会打开

3. **识别表单字段**
   - 点击"🔍 识别字段"按钮
   - 插件会自动扫描页面中的表单字段
   - 状态栏会显示识别到的字段数量

4. **填充表单**
   - 点击"✨ 填充表单"按钮
   - 插件会根据配置自动填充表单字段
   - 填充完成后会显示成功填充的字段数量

### 高级配置

1. **打开高级设置**
   - 在插件弹窗中点击"⚙️ 高级设置"按钮

2. **配置选项说明**
   - **模板选择**：选择预设的填充模板（默认/餐饮/零售）
   - **自动填充选项**：控制是否自动填充下拉框、日期、复选框
   - **品牌经营范围**：设置默认的经营范围文本
   - **日期偏移设置**：配置各种日期字段相对于当前日期的偏移天数
   - **填充设置**：控制填充延迟、高亮显示等选项

3. **保存配置**
   - 修改配置后点击"💾 保存配置"按钮
   - 配置会自动保存到浏览器本地存储

## 🔧 故障排除

### 常见问题

**Q: 插件图标不显示**
- A: 检查是否正确启用了开发者模式
- A: 尝试刷新扩展程序管理页面
- A: 重新加载插件文件夹

**Q: 无法识别表单字段**
- A: 确保页面已完全加载
- A: 检查页面是否包含标准的HTML表单元素
- A: 尝试刷新页面后重新识别

**Q: 填充失败或部分失败**
- A: 检查表单字段是否被禁用或隐藏
- A: 尝试调整填充延迟设置
- A: 查看浏览器控制台是否有错误信息

**Q: 配置无法保存**
- A: 检查浏览器是否允许扩展程序访问存储
- A: 尝试重新安装插件

### 调试模式

1. **启用调试日志**
   - 在高级设置中勾选"启用调试日志"
   - 打开浏览器开发者工具（F12）
   - 查看Console标签页中的详细日志

2. **性能监控**
   - 插件会自动记录操作性能指标
   - 在控制台中输入以下命令查看性能报告：
   ```javascript
   // 在插件弹窗的控制台中执行
   console.log(window.popupController?.getPerformanceReport());
   ```

## 📋 支持的字段类型

### 自动识别的字段

- **文本输入框**：`<input type="text">`
- **邮箱输入框**：`<input type="email">`
- **电话输入框**：`<input type="tel">`
- **日期选择器**：`<input type="date">`
- **下拉选择框**：`<select>`
- **复选框**：`<input type="checkbox">`
- **单选框**：`<input type="radio">`
- **文本域**：`<textarea>`

### 字段识别规则

插件通过以下方式识别字段：
1. **ID属性**：优先匹配元素的ID
2. **Name属性**：匹配元素的name属性
3. **标签文本**：匹配关联的label标签文本
4. **占位符文本**：匹配placeholder属性

## 🔒 隐私与安全

- **本地存储**：所有配置和数据仅存储在本地浏览器中
- **无网络请求**：插件不会向外部服务器发送任何数据
- **权限最小化**：仅请求必要的浏览器权限
- **开源透明**：所有代码都是开源的，可以自由审查

## 📞 技术支持

如果遇到问题或需要帮助，请：

1. **查看日志**：启用调试模式查看详细错误信息
2. **重现问题**：在测试页面上尝试重现问题
3. **收集信息**：记录浏览器版本、操作系统、错误信息等
4. **联系支持**：通过GitHub Issues或邮件联系技术支持

---

**版本信息**：v1.0.0  
**更新日期**：2024年  
**兼容性**：Chrome 88+, Edge 88+  
**许可证**：MIT License