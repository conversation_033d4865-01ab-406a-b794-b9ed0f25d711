/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    width: 380px;
    max-height: 600px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.subtitle {
    font-size: 12px;
    opacity: 0.9;
}

/* 主内容区域 */
.main-content {
    padding: 20px;
    max-height: 450px;
    overflow-y: auto;
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 区块样式 */
.section {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.section h3 {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
}

.section h4 {
    font-size: 14px;
    color: #34495e;
    margin-bottom: 10px;
    font-weight: 500;
}

/* 表单控件样式 */
.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #2c3e50;
}

/* 配置项样式 */
.config-item {
    margin-bottom: 10px;
}

.config-item label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.config-item label:hover {
    background-color: #f8f9fa;
}

.config-item input[type="checkbox"] {
    margin-right: 10px;
    width: 16px;
    height: 16px;
    accent-color: #4facfe;
}

/* 按钮样式 */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    min-width: 100px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
    transform: translateY(-1px);
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background: #e0a800;
    transform: translateY(-1px);
}

.btn-link {
    background: transparent;
    color: #4facfe;
    border: 1px solid #4facfe;
    padding: 8px 16px;
    font-size: 13px;
}

.btn-link:hover {
    background: #4facfe;
    color: white;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.button-group .btn {
    flex: 1;
}

/* 状态显示样式 */
.status {
    background: #f8f9fa;
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
}

.status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.status-item:last-child {
    margin-bottom: 0;
}

.status-label {
    font-weight: 500;
    color: #6c757d;
}

.status-value {
    font-weight: 600;
    color: #28a745;
}

/* 高级配置面板 */
.advanced-panel {
    background: #f8f9fa;
    padding: 20px;
    border-top: 1px solid #dee2e6;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.config-group {
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.input-group label {
    flex: 1;
    margin-bottom: 0;
    margin-right: 10px;
}

.input-group input[type="number"] {
    width: 80px;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
    transition: border-color 0.2s ease;
}

.input-group input[type="number"]:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 2px rgba(79, 172, 254, 0.1);
}

/* 填充延迟输入框特殊样式 */
#fill-delay {
    width: 100px;
}

/* 配置项增强样式 */
.config-item {
    position: relative;
}

.config-item input[type="checkbox"] {
    position: relative;
    cursor: pointer;
}

.config-item input[type="checkbox"]:checked {
    background-color: #4facfe;
}

/* 配置组标题样式增强 */
.config-group h4 {
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
    margin-bottom: 15px;
    color: #495057;
}

.priority-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.priority-list label {
    margin-bottom: 0;
    font-weight: normal;
    font-size: 13px;
}

/* 帮助信息样式 */
.help-section {
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

details {
    cursor: pointer;
}

summary {
    font-weight: 500;
    color: #495057;
    padding: 5px 0;
    outline: none;
}

summary:hover {
    color: #4facfe;
}

.help-content {
    margin-top: 10px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.6;
}

.help-content ol, .help-content ul {
    margin-left: 20px;
    margin-bottom: 10px;
}

.help-content li {
    margin-bottom: 5px;
}

.help-content p {
    margin-bottom: 8px;
}

/* 响应式调整 */
@media (max-width: 400px) {
    .container {
        width: 100vw;
        height: 100vh;
        border-radius: 0;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .input-group input[type="number"] {
        width: 100%;
        margin-top: 5px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #4facfe;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 成功/错误状态 */
.status-success {
    color: #28a745 !important;
}

.status-error {
    color: #dc3545 !important;
}

.status-warning {
    color: #ffc107 !important;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}