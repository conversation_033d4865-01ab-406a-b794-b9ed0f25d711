<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>调试测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 350px;
            height: 400px;
            background: #ffffff;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        
        .debug-container {
            width: 100%;
            height: 100%;
            background: #f5f5f5;
            border: 2px solid #007cba;
            border-radius: 8px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        h1 {
            color: #007cba;
            font-size: 18px;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .button {
            width: 100%;
            height: 40px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 14px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .button:hover {
            background: #005a87;
        }
        
        .button.secondary {
            background: #28a745;
        }
        
        .button.secondary:hover {
            background: #218838;
        }
        
        .status {
            background: white;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            text-align: center;
            font-size: 12px;
        }
        
        .info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 插件调试测试</h1>
        
        <div class="info">
            <strong>调试信息：</strong><br>
            如果你能看到这个界面和下面的按钮，说明插件基本功能正常。
        </div>
        
        <button class="button" id="test-btn-1">
            🎯 测试按钮 1
        </button>
        
        <button class="button secondary" id="test-btn-2">
            👀 测试按钮 2
        </button>
        
        <div class="status" id="status">
            状态：等待测试...
        </div>
        
        <div class="info">
            <strong>尺寸信息：</strong><br>
            宽度: 350px, 高度: 400px
        </div>
    </div>

    <script src="popup-debug-simple.js"></script>
</body>
</html>
