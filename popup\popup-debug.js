/**
 * 简化版本用于调试插件按钮显示问题
 */

console.log('Popup debug script loaded');

// 等待DOM加载完成
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded successfully');
    
    // 检查关键按钮是否存在
    const fillButton = document.getElementById('fill-form');
    const previewButton = document.getElementById('preview-form');
    const advancedButton = document.getElementById('advanced-config');
    
    console.log('Fill button found:', fillButton);
    console.log('Preview button found:', previewButton);
    console.log('Advanced button found:', advancedButton);
    
    // 为按钮添加简单的点击事件
    if (fillButton) {
        fillButton.addEventListener('click', function() {
            console.log('填充按钮被点击');
            alert('填充按钮工作正常！');
        });
        console.log('Fill button event listener added');
    } else {
        console.error('Fill button not found!');
    }
    
    if (previewButton) {
        previewButton.addEventListener('click', function() {
            console.log('预览按钮被点击');
            alert('预览按钮工作正常！');
        });
        console.log('Preview button event listener added');
    } else {
        console.error('Preview button not found!');
    }
    
    if (advancedButton) {
        advancedButton.addEventListener('click', function() {
            console.log('高级设置按钮被点击');
            alert('高级设置按钮工作正常！');
        });
        console.log('Advanced button event listener added');
    } else {
        console.error('Advanced button not found!');
    }
    
    // 检查所有按钮元素
    const allButtons = document.querySelectorAll('button');
    console.log('Total buttons found:', allButtons.length);
    allButtons.forEach((btn, index) => {
        console.log(`Button ${index}:`, btn.id, btn.textContent);
    });
    
    // 检查CSS是否加载
    const computedStyle = window.getComputedStyle(document.body);
    console.log('Body font-family:', computedStyle.fontFamily);
    
    // 显示调试信息
    const statusText = document.getElementById('status-text');
    if (statusText) {
        statusText.textContent = '调试模式';
    }
    
    console.log('Debug initialization completed');
});

// 检查是否有JavaScript错误
window.addEventListener('error', function(e) {
    console.error('JavaScript Error:', e.error);
    alert('发现JavaScript错误: ' + e.message);
});

// 检查未处理的Promise拒绝
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled Promise Rejection:', e.reason);
    alert('发现Promise错误: ' + e.reason);
});