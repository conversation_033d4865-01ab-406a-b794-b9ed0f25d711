/**
 * 表单自动填充插件 - 弹窗脚本
 * 负责用户界面交互和与内容脚本通信
 */

class PopupController {
    constructor() {
        this.currentTab = null;
        this.config = this.getDefaultConfig();
        this.fieldCount = 0;
        this.performanceMetrics = {
            startTime: Date.now(),
            operations: [],
            errors: []
        };
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }

    /**
     * 初始化弹窗控制器
     */
    async init() {
        const startTime = Date.now();
        try {
            // 获取当前标签页
            await this.getCurrentTab();
            
            // 加载保存的配置
            await this.loadConfig();
            
            // 绑定事件监听器
            this.bindEvents();
            
            // 更新界面状态
            this.updateUI();
            
            // 自动检测表单字段
            await this.previewForm();
            
            // 记录性能指标
            this.recordPerformance('init', Date.now() - startTime);
            
            console.log('弹窗初始化完成');
        } catch (error) {
            this.handleError('初始化', error);
            await this.retryOperation(() => this.init());
        }
    }

    /**
     * 获取当前活动标签页
     */
    async getCurrentTab() {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        this.currentTab = tab;
        return tab;
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            template: 'default',
            autoDropdown: true,
            autoDate: true,
            autoCheckbox: true,
            brandScope: '测试',
            dateOffsets: {
                delivery: 7,
                rent: 14,
                opening: 30
            },
            fieldPriority: {
                id: true,
                name: true,
                label: true,
                placeholder: false
            },
            fillDelay: 100,
            highlightFields: true,
            enableLogging: true,
            confirmBeforeFill: false
        };
    }

    /**
     * 加载保存的配置
     */
    async loadConfig() {
        try {
            const result = await chrome.storage.local.get('formConfig');
            if (result.formConfig) {
                this.config = { ...this.config, ...result.formConfig };
            }
        } catch (error) {
            console.error('加载配置失败:', error);
        }
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        try {
            await chrome.storage.local.set({ formConfig: this.config });
            this.showStatus('配置已保存', 'success');
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showStatus('保存失败', 'error');
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 主要操作按钮
        document.getElementById('fill-form').addEventListener('click', () => this.fillForm());
        document.getElementById('preview-form').addEventListener('click', () => this.previewForm());
        
        // 高级设置
        document.getElementById('advanced-config').addEventListener('click', () => this.toggleAdvancedPanel());
        document.getElementById('save-config').addEventListener('click', () => this.saveCurrentConfig());
        document.getElementById('reset-config').addEventListener('click', () => this.resetConfig());
        
        // 配置项变化监听
        document.getElementById('template-select').addEventListener('change', (e) => {
            this.config.template = e.target.value;
            this.applyTemplate(e.target.value);
        });
        
        document.getElementById('auto-dropdown').addEventListener('change', (e) => {
            this.config.autoDropdown = e.target.checked;
        });
        
        document.getElementById('auto-date').addEventListener('change', (e) => {
            this.config.autoDate = e.target.checked;
        });
        
        document.getElementById('auto-checkbox').addEventListener('change', (e) => {
            this.config.autoCheckbox = e.target.checked;
        });
        
        document.getElementById('brand-scope').addEventListener('input', (e) => {
            this.config.brandScope = e.target.value;
        });
        
        // 日期偏移设置
        document.getElementById('delivery-offset').addEventListener('change', (e) => {
            this.config.dateOffsets.delivery = parseInt(e.target.value) || 0;
        });
        
        document.getElementById('rent-offset').addEventListener('change', (e) => {
            this.config.dateOffsets.rent = parseInt(e.target.value) || 0;
        });
        
        document.getElementById('opening-offset').addEventListener('change', (e) => {
            this.config.dateOffsets.opening = parseInt(e.target.value) || 0;
        });
        
        // 新增配置项监听
        document.getElementById('fill-delay').addEventListener('change', (e) => {
            this.config.fillDelay = parseInt(e.target.value) || 100;
        });
        
        document.getElementById('highlight-fields').addEventListener('change', (e) => {
            this.config.highlightFields = e.target.checked;
        });
        
        document.getElementById('enable-logging').addEventListener('change', (e) => {
            this.config.enableLogging = e.target.checked;
        });
        
        document.getElementById('confirm-before-fill').addEventListener('change', (e) => {
            this.config.confirmBeforeFill = e.target.checked;
        });
    }

    /**
     * 更新界面显示
     */
    updateUI() {
        // 更新配置项显示
        document.getElementById('template-select').value = this.config.template;
        document.getElementById('auto-dropdown').checked = this.config.autoDropdown;
        document.getElementById('auto-date').checked = this.config.autoDate;
        document.getElementById('auto-checkbox').checked = this.config.autoCheckbox;
        document.getElementById('brand-scope').value = this.config.brandScope;
        
        // 更新高级设置
        document.getElementById('delivery-offset').value = this.config.dateOffsets.delivery;
        document.getElementById('rent-offset').value = this.config.dateOffsets.rent;
        document.getElementById('opening-offset').value = this.config.dateOffsets.opening;
        
        // 更新新增配置项
        if (document.getElementById('fill-delay')) {
            document.getElementById('fill-delay').value = this.config.fillDelay;
        }
        if (document.getElementById('highlight-fields')) {
            document.getElementById('highlight-fields').checked = this.config.highlightFields;
        }
        if (document.getElementById('enable-logging')) {
            document.getElementById('enable-logging').checked = this.config.enableLogging;
        }
        if (document.getElementById('confirm-before-fill')) {
            document.getElementById('confirm-before-fill').checked = this.config.confirmBeforeFill;
        }
    }

    /**
     * 显示状态信息
     */
    showStatus(message, type = 'info') {
        const statusElement = document.getElementById('status-text');
        statusElement.textContent = message;
        
        // 移除之前的状态类
        statusElement.classList.remove('status-success', 'status-error', 'status-warning');
        
        // 添加新的状态类
        if (type === 'success') {
            statusElement.classList.add('status-success');
        } else if (type === 'error') {
            statusElement.classList.add('status-error');
        } else if (type === 'warning') {
            statusElement.classList.add('status-warning');
        }
    }

    /**
     * 更新字段计数显示
     */
    updateFieldCount(count) {
        this.fieldCount = count;
        document.getElementById('field-count').textContent = count;
    }





    /**
     * 向内容脚本发送消息
     */
    async sendMessageToContent(message) {
        try {
            if (!this.currentTab || !this.currentTab.id) {
                throw new Error('无效的标签页');
            }
            
            const response = await chrome.tabs.sendMessage(this.currentTab.id, message);
            return response;
        } catch (error) {
            console.error('发送消息失败:', error);
            throw error;
        }
    }

    /**
     * 切换高级设置面板
     */
    toggleAdvancedPanel() {
        const panel = document.getElementById('advanced-panel');
        const isVisible = panel.style.display !== 'none';
        
        panel.style.display = isVisible ? 'none' : 'block';
        
        const button = document.getElementById('advanced-config');
        button.textContent = isVisible ? '⚙️ 高级设置' : '🔙 收起设置';
    }

    /**
     * 保存当前配置
     */
    async saveCurrentConfig() {
        // 收集当前界面的所有配置
        this.config.template = document.getElementById('template-select').value;
        this.config.autoDropdown = document.getElementById('auto-dropdown').checked;
        this.config.autoDate = document.getElementById('auto-date').checked;
        this.config.autoCheckbox = document.getElementById('auto-checkbox').checked;
        this.config.brandScope = document.getElementById('brand-scope').value;
        
        this.config.dateOffsets.delivery = parseInt(document.getElementById('delivery-offset').value) || 0;
        this.config.dateOffsets.rent = parseInt(document.getElementById('rent-offset').value) || 0;
        this.config.dateOffsets.opening = parseInt(document.getElementById('opening-offset').value) || 0;
        
        await this.saveConfig();
    }

    /**
     * 应用模板配置
     */
    applyTemplate(templateName) {
        const templates = {
            'default': {
                brandScope: '测试',
                dateOffsets: { delivery: 7, rent: 14, opening: 30 }
            },
            'restaurant': {
                brandScope: '测试',
                dateOffsets: { delivery: 10, rent: 20, opening: 45 }
            },
            'retail': {
                brandScope: '测试',
                dateOffsets: { delivery: 5, rent: 10, opening: 21 }
            }
        };
        
        const template = templates[templateName];
        if (template) {
            this.config.brandScope = template.brandScope;
            this.config.dateOffsets = { ...this.config.dateOffsets, ...template.dateOffsets };
            this.updateUI();
            this.showStatus(`已应用${templateName}模板`, 'success');
        }
    }



    /**
     * 重置为默认配置
     */
    async resetConfig() {
        if (confirm('确定要重置为默认配置吗？')) {
            this.config = this.getDefaultConfig();
            this.updateUI();
            await this.saveConfig();
            this.showStatus('已重置为默认配置', 'success');
        }
    }

    /**
     * 记录性能指标
     */
    recordPerformance(operation, duration, additionalData = {}) {
        const metric = {
            operation,
            duration,
            timestamp: Date.now(),
            ...additionalData
        };
        
        this.performanceMetrics.operations.push(metric);
        
        // 保持最近100条记录
        if (this.performanceMetrics.operations.length > 100) {
            this.performanceMetrics.operations.shift();
        }
        
        // 记录慢操作
        if (duration > 1000) {
            console.warn(`慢操作检测: ${operation} 耗时 ${duration}ms`);
        }
    }

    /**
     * 统一错误处理
     */
    handleError(context, error, showToUser = true) {
        const errorInfo = {
            context,
            message: error.message,
            stack: error.stack,
            timestamp: Date.now(),
            url: window.location.href,
            userAgent: navigator.userAgent
        };
        
        this.performanceMetrics.errors.push(errorInfo);
        
        // 保持最近50条错误记录
        if (this.performanceMetrics.errors.length > 50) {
            this.performanceMetrics.errors.shift();
        }
        
        console.error(`${context}错误:`, error);
        
        if (showToUser) {
            this.showStatus(`${context}失败`, 'error');
        }
        
        // 发送错误报告（可选）
        this.reportError(errorInfo);
    }

    /**
     * 重试操作
     */
    async retryOperation(operation, maxRetries = this.maxRetries) {
        if (this.retryCount >= maxRetries) {
            this.showStatus('操作失败，已达到最大重试次数', 'error');
            this.retryCount = 0;
            return false;
        }
        
        this.retryCount++;
        const delay = Math.min(1000 * Math.pow(2, this.retryCount - 1), 5000); // 指数退避
        
        this.showStatus(`正在重试... (${this.retryCount}/${maxRetries})`, 'warning');
        
        await this.delay(delay);
        
        try {
            await operation();
            this.retryCount = 0;
            return true;
        } catch (error) {
            return await this.retryOperation(operation, maxRetries);
        }
    }

    /**
     * 延迟函数
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 错误报告
     */
    async reportError(errorInfo) {
        try {
            // 保存到本地存储用于调试
            const errorLogs = await chrome.storage.local.get('errorLogs') || { errorLogs: [] };
            errorLogs.errorLogs.push(errorInfo);
            
            // 保持最近100条错误日志
            if (errorLogs.errorLogs.length > 100) {
                errorLogs.errorLogs = errorLogs.errorLogs.slice(-100);
            }
            
            await chrome.storage.local.set({ errorLogs: errorLogs.errorLogs });
        } catch (error) {
            console.error('保存错误日志失败:', error);
        }
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        const totalTime = Date.now() - this.performanceMetrics.startTime;
        const operations = this.performanceMetrics.operations;
        const errors = this.performanceMetrics.errors;
        
        const avgDuration = operations.length > 0 
            ? operations.reduce((sum, op) => sum + op.duration, 0) / operations.length 
            : 0;
        
        return {
            totalTime,
            operationCount: operations.length,
            errorCount: errors.length,
            avgOperationDuration: Math.round(avgDuration),
            slowOperations: operations.filter(op => op.duration > 1000),
            recentErrors: errors.slice(-10)
        };
    }

    /**
     * 增强的预览表单功能
     */
    async previewForm() {
        const startTime = Date.now();
        try {
            this.showStatus('正在识别表单字段...', 'info');
            
            const response = await this.sendMessageToContent({
                action: 'previewFields',
                config: this.config
            });
            
            if (response && response.success) {
                this.updateFieldCount(response.fieldCount);
                this.showStatus(`识别到 ${response.fieldCount} 个字段`, 'success');
                
                // 显示字段详情（如果有）
                if (response.fieldDetails) {
                    this.displayFieldDetails(response.fieldDetails);
                }
            } else {
                this.showStatus('未找到可填充字段', 'warning');
                this.updateFieldCount(0);
            }
            
            this.recordPerformance('previewForm', Date.now() - startTime, {
                fieldCount: response?.fieldCount || 0
            });
        } catch (error) {
            this.handleError('预览表单', error);
            this.updateFieldCount(0);
        }
    }

    /**
     * 显示字段详情
     */
    displayFieldDetails(fieldDetails) {
        // 这里可以添加显示字段详情的逻辑
        // 例如在状态区域显示字段类型统计
        const fieldTypes = {};
        fieldDetails.forEach(field => {
            fieldTypes[field.type] = (fieldTypes[field.type] || 0) + 1;
        });
        
        const typesSummary = Object.entries(fieldTypes)
            .map(([type, count]) => `${type}(${count})`)
            .join(', ');
        
        console.log('字段类型统计:', typesSummary);
    }

    /**
     * 增强的表单填充功能
     */
    async fillForm() {
        const startTime = Date.now();
        try {
            if (this.fieldCount === 0) {
                this.showStatus('请先识别表单字段', 'warning');
                return;
            }
            
            // 确认填充
            if (this.config.confirmBeforeFill) {
                if (!confirm(`确定要填充 ${this.fieldCount} 个字段吗？`)) {
                    return;
                }
            }
            
            this.showStatus('正在填充表单...', 'info');
            
            const response = await this.sendMessageToContent({
                action: 'fillForm',
                config: this.config
            });
            
            if (response && response.success) {
                const successRate = ((response.filledCount / this.fieldCount) * 100).toFixed(1);
                this.showStatus(`成功填充 ${response.filledCount}/${this.fieldCount} 个字段 (${successRate}%)`, 'success');
                
                // 显示详细结果
                if (response.errors && response.errors.length > 0) {
                    console.warn('填充过程中的错误:', response.errors);
                }
                
                // 记录使用统计
                this.recordUsage(response.filledCount, response.errors?.length || 0);
            } else {
                this.showStatus('填充失败', 'error');
            }
            
            this.recordPerformance('fillForm', Date.now() - startTime, {
                fieldCount: this.fieldCount,
                filledCount: response?.filledCount || 0,
                errorCount: response?.errors?.length || 0
            });
        } catch (error) {
            this.handleError('填充表单', error);
        }
    }

    /**
     * 增强的使用统计记录
     */
    async recordUsage(filledCount, errorCount = 0) {
        try {
            const stats = await chrome.storage.local.get('usageStats') || {};
            const today = new Date().toDateString();
            
            if (!stats.usageStats) {
                stats.usageStats = {};
            }
            
            if (!stats.usageStats[today]) {
                stats.usageStats[today] = { 
                    uses: 0, 
                    fields: 0, 
                    errors: 0,
                    successRate: 0
                };
            }
            
            const todayStats = stats.usageStats[today];
            todayStats.uses++;
            todayStats.fields += filledCount;
            todayStats.errors += errorCount;
            todayStats.successRate = todayStats.fields > 0 
                ? ((todayStats.fields - todayStats.errors) / todayStats.fields * 100).toFixed(1)
                : 0;
            
            await chrome.storage.local.set(stats);
        } catch (error) {
            console.error('记录使用统计失败:', error);
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new PopupController();
});

// 错误处理
window.addEventListener('error', (event) => {
    console.error('弹窗脚本错误:', event.error);
});

// 未处理的Promise拒绝
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});