# 🚀 插件安装和测试指南

## 快速安装步骤

### 1. 安装插件
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹：`f:\桌面\plugin`
6. 确认插件已成功加载并启用

### 2. 验证安装
- 在浏览器工具栏应该能看到插件图标
- 插件名称显示为"表单自动填充助手"
- 状态显示为"已启用"

## 🧪 测试插件功能

### 方法一：使用测试页面
1. 双击打开 `test-form.html` 文件
2. 页面会在浏览器中打开，显示一个测试表单
3. 点击浏览器工具栏中的插件图标
4. 在弹出窗口中：
   - 点击"👀 预览识别"按钮
   - 查看状态栏显示识别到的字段数量
   - 点击"🎯 一键填充"按钮
   - 观察表单是否被自动填充

### 方法二：使用真实网站
1. 打开任何包含表单的网站
2. 点击插件图标
3. 测试预览和填充功能

## 🔍 故障排除

### 问题1：插件图标不显示
**解决方案：**
- 确保开发者模式已开启
- 重新加载插件
- 检查文件路径是否正确

### 问题2：点击图标没有弹窗
**解决方案：**
1. 右键点击插件图标
2. 选择"检查弹出式窗口"
3. 查看控制台错误信息
4. 应该看到以下日志：
   ```
   简化版插件开始加载...
   DOM加载完成，开始初始化插件...
   开始初始化...
   绑定事件监听器...
   填充按钮事件已绑定
   预览按钮事件已绑定
   初始化完成
   ```

### 问题3：按钮不响应
**检查步骤：**
1. 确认弹窗中能看到两个按钮：
   - 🎯 一键填充
   - 👀 预览识别
2. 点击按钮时查看控制台日志
3. 确认没有JavaScript错误

### 问题4：无法识别表单字段
**可能原因：**
- 网站使用了特殊的表单控件
- 页面还未完全加载
- 权限问题

**解决方案：**
- 等待页面完全加载后再试
- 刷新页面后重试
- 使用提供的测试页面验证基本功能

## 📋 功能验证清单

### ✅ 基础功能
- [ ] 插件成功安装
- [ ] 插件图标显示正常
- [ ] 点击图标弹出窗口
- [ ] 弹窗显示完整界面
- [ ] 按钮可以点击

### ✅ 核心功能
- [ ] 预览识别功能工作
- [ ] 状态栏显示字段数量
- [ ] 一键填充功能工作
- [ ] 表单字段被正确填充

### ✅ 高级功能
- [ ] 模板选择功能
- [ ] 自定义文本输入
- [ ] 状态信息正确显示

## 🐛 常见错误和解决方案

### CSP错误
```
Refused to execute inline script because it violates the following Content Security Policy directive
```
**解决方案：** 已修复，确保使用最新版本的文件

### 权限错误
```
Cannot access chrome.tabs
```
**解决方案：** 确保manifest.json中包含"activeTab"权限

### 通信错误
```
Could not establish connection
```
**解决方案：** 
- 确保content.js正确加载
- 检查网站是否阻止了脚本注入
- 尝试刷新页面

## 📞 获取帮助

如果遇到问题，请提供：
1. 具体的错误信息
2. 浏览器控制台截图
3. Chrome版本号
4. 操作系统信息

## 🎯 预期结果

正常工作时，插件应该能够：
1. 自动识别页面中的表单字段
2. 根据字段类型智能填充数据
3. 显示填充进度和结果
4. 提供友好的用户界面

---

**测试完成后，记得将manifest.json改回使用完整版本：**
```json
"default_popup": "popup/popup.html"
```
