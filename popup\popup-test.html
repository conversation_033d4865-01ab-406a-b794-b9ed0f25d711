<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件测试弹窗</title>
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h3 {
            margin: 0 0 20px 0;
            color: #333;
            text-align: center;
        }
        
        button {
            width: 100%;
            padding: 12px 20px;
            margin: 8px 0;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007cba;
            color: white;
        }
        
        .btn-primary:hover {
            background: #005a87;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
            transform: translateY(-1px);
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h3>🚀 插件功能测试</h3>
        
        <button id="test-btn" class="btn-primary">
            🎯 测试按钮
        </button>
        
        <button id="fill-btn" class="btn-secondary">
            📝 模拟填充
        </button>
        
        <button id="preview-btn" class="btn-secondary">
            👀 模拟预览
        </button>
        
        <div class="status" id="status">
            状态: 等待测试
        </div>
    </div>
    
    <script>
        console.log('Test popup loaded');
        
        // 测试基本功能
        document.getElementById('test-btn').onclick = function() {
            console.log('Test button clicked');
            document.getElementById('status').textContent = '状态: 测试按钮工作正常！';
            alert('✅ 测试按钮工作正常！\n\n这说明插件的基本结构是正确的。');
        };
        
        // 模拟填充功能
        document.getElementById('fill-btn').onclick = function() {
            console.log('Fill button clicked');
            document.getElementById('status').textContent = '状态: 填充功能测试';
            alert('📝 模拟填充功能\n\n如果看到这个消息，说明按钮事件正常工作。');
        };
        
        // 模拟预览功能
        document.getElementById('preview-btn').onclick = function() {
            console.log('Preview button clicked');
            document.getElementById('status').textContent = '状态: 预览功能测试';
            alert('👀 模拟预览功能\n\n按钮响应正常，可以进行下一步调试。');
        };
        
        // 页面加载完成提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test popup DOM loaded');
            document.getElementById('status').textContent = '状态: 页面加载完成，可以测试';
        });
        
        // 错误捕获
        window.addEventListener('error', function(e) {
            console.error('Error in test popup:', e);
            document.getElementById('status').textContent = '状态: 发现错误 - ' + e.message;
        });
    </script>
</body>
</html>