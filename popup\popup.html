<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单自动填充</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h2>🚀 表单自动填充</h2>
            <p class="subtitle">让测试更高效</p>
        </div>

        <!-- 主要操作区域 -->
        <div class="main-content">
            <!-- 模板选择 -->
            <div class="section">
                <label for="template-select">选择模板:</label>
                <select id="template-select" class="form-control">
                    <option value="default">默认模板</option>
                    <option value="restaurant">餐饮模板</option>
                    <option value="retail">零售模板</option>
                </select>
            </div>

            <!-- 快速配置 -->
            <div class="section">
                <h3>快速配置</h3>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="auto-dropdown" checked>
                        自动选择下拉框第一项
                    </label>
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="auto-date" checked>
                        自动填充当前日期
                    </label>
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="auto-checkbox" checked>
                        默认选择"是"选项
                    </label>
                </div>
            </div>

            <!-- 自定义文本 -->
            <div class="section">
                <label for="brand-scope">品牌经营范围:</label>
                <input type="text" id="brand-scope" class="form-control" 
                       placeholder="例如: 测试" value="测试">
            </div>

            <!-- 操作按钮 -->
            <div class="button-group">
                <button id="fill-form" class="btn btn-primary">
                    🎯 一键填充
                </button>
                <button id="preview-form" class="btn btn-secondary">
                    👀 预览识别
                </button>
            </div>

            <!-- 高级设置 -->
            <div class="section">
                <button id="advanced-config" class="btn btn-link">
                    ⚙️ 高级设置
                </button>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="status" id="status">
            <div class="status-item">
                <span class="status-label">状态:</span>
                <span class="status-value" id="status-text">就绪</span>
            </div>
            <div class="status-item">
                <span class="status-label">识别字段:</span>
                <span class="status-value" id="field-count">0</span>
            </div>
        </div>

        <!-- 高级配置面板 (默认隐藏) -->
        <div class="advanced-panel" id="advanced-panel" style="display: none;">
            <h3>高级配置</h3>
            
            <div class="config-group">
                <h4>日期偏移设置 (天数)</h4>
                <div class="input-group">
                    <label>交付日期:</label>
                    <input type="number" id="delivery-offset" value="7" min="0">
                </div>
                <div class="input-group">
                    <label>计租日期:</label>
                    <input type="number" id="rent-offset" value="14" min="0">
                </div>
                <div class="input-group">
                    <label>开业日期:</label>
                    <input type="number" id="opening-offset" value="30" min="0">
                </div>
            </div>

            <div class="config-group">
                <h4>填充设置</h4>
                <div class="input-group">
                    <label>填充延迟(毫秒):</label>
                    <input type="number" id="fill-delay" value="100" min="0" max="5000">
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="highlight-fields" checked>
                        高亮显示正在填充的字段
                    </label>
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="enable-logging" checked>
                        启用调试日志
                    </label>
                </div>
                <div class="config-item">
                    <label>
                        <input type="checkbox" id="confirm-before-fill">
                        填充前确认
                    </label>
                </div>
            </div>

            <div class="config-group">
                <h4>字段识别优先级</h4>
                <div class="priority-list">
                    <label>
                        <input type="checkbox" checked> ID属性匹配
                    </label>
                    <label>
                        <input type="checkbox" checked> Name属性匹配
                    </label>
                    <label>
                        <input type="checkbox" checked> 标签文本匹配
                    </label>
                    <label>
                        <input type="checkbox"> Placeholder匹配
                    </label>
                </div>
            </div>

            <div class="button-group">
                <button id="save-config" class="btn btn-success">
                    💾 保存配置
                </button>
                <button id="reset-config" class="btn btn-warning">
                    🔄 重置默认
                </button>
            </div>
        </div>

        <!-- 帮助信息 -->
        <div class="help-section">
            <details>
                <summary>📖 使用说明</summary>
                <div class="help-content">
                    <ol>
                        <li>打开需要填写的表单页面</li>
                        <li>点击"预览识别"查看可填充字段</li>
                        <li>调整配置选项(可选)</li>
                        <li>点击"一键填充"自动填写表单</li>
                    </ol>
                    <p><strong>支持的字段类型:</strong></p>
                    <ul>
                        <li>文本输入框</li>
                        <li>下拉选择框</li>
                        <li>复选框和单选框</li>
                        <li>日期选择器</li>
                    </ul>
                </div>
            </details>
        </div>
    </div>

    <!-- 临时使用调试脚本排查问题 -->
    <script src="popup-debug.js"></script>
</body>
</html>