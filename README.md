# Chrome表单自动填充插件

## 项目背景

在测试过程中，经常需要重复填写各种表单信息，这个过程非常繁琐且耗时。本插件旨在自动化填写常见的表单字段，提高测试效率。

## 功能概述

### 支持的表单字段类型

1. **签约品牌经营范围** - 文本输入框，默认输入“测试”
2. **下拉框字段** - 自动选择第一个选项
   - 商铺类型
   - 供应商类型
   - 签约主体
   - 经营模式
   - 租赁类型
   - 租金递增方式
   - 月运营管理费总额递增方式
   - 月物业管理费总额递增方式
3. **数字选择** - 0或1的选择，默认选择"是"(1)
   - 是否重新装修
4. **时间起始** - 默认设置为当天
   - 租赁起止时间
5. **单个时间选择** - 默认设置为当天
   - 商户交付日期
   - 商户计租日期
   - 商户开业日期

## 项目结构

```
plugin/
├── README.md              # 项目说明文档
├── manifest.json          # Chrome扩展配置文件
├── popup/                 # 弹窗界面
│   ├── popup.html         # 弹窗HTML
│   ├── popup.css          # 弹窗样式
│   └── popup.js           # 弹窗逻辑
├── content/               # 内容脚本
│   └── content.js         # 页面内容操作脚本
├── background/            # 后台脚本
│   └── background.js      # 后台服务脚本
├── config/                # 配置文件
│   └── form-config.json   # 表单配置数据
└── icons/                 # 图标资源
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

## 使用方法

1. **安装插件**
   - 打开Chrome浏览器
   - 进入扩展程序管理页面 (chrome://extensions/)
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择插件文件夹

2. **使用插件**
   - 打开需要填写的表单页面
   - 点击浏览器工具栏中的插件图标
   - 在弹出的界面中选择填充模板
   - 点击"自动填充"按钮
   - 插件将自动识别并填充表单字段

3. **自定义配置**
   - 在弹窗界面中点击"配置"按钮
   - 修改默认值和填充规则
   - 保存配置供下次使用

## 核心功能模块

### 1. 表单识别模块
- **功能**: 自动识别页面中的表单字段
- **实现**: 通过CSS选择器和XPath匹配
- **特点**: 支持多种网站结构适配

### 2. 数据配置模块
- **功能**: 管理填充数据和规则
- **配置项**:
  - 默认文本内容
  - 下拉框选择规则
  - 日期设置规则
  - 复选框默认状态

### 3. 自动填充模块
- **功能**: 执行实际的表单填充操作
- **支持类型**:
  - 文本输入框 (input[type="text"])
  - 下拉选择框 (select)
  - 复选框 (input[type="checkbox"])
  - 单选框 (input[type="radio"])
  - 日期选择器 (input[type="date"])

### 4. 用户界面模块
- **弹窗界面**: 简洁的操作面板
- **配置界面**: 数据和规则管理
- **状态反馈**: 填充结果提示

## 配置文件说明

### form-config.json 结构

```json
{
  "templates": {
    "default": {
      "name": "默认模板",
      "fields": {
        "brandScope": "餐饮服务",
        "dropdowns": {
          "selectFirst": true
        },
        "checkboxes": {
          "renovation": true
        },
        "dates": {
          "useCurrentDate": ["leaseStart", "leaseEnd"],
          "customOffsets": {
            "deliveryDate": 7,
            "rentStartDate": 14,
            "openingDate": 30
          }
        }
      }
    }
  }
}
```

## 开发阶段

### 第一阶段：基础框架 ✅
- [x] 创建项目结构
- [x] 配置manifest.json
- [x] 实现基础弹窗界面
- [x] 建立内容脚本通信
- [x] 创建后台服务脚本
- [x] 设计配置文件结构
- [x] 制作扩展图标

### 第二阶段：核心功能
- [ ] 实现表单字段识别
- [ ] 开发填充逻辑
- [ ] 创建配置管理
<!-- 注意！这块就是因为调试的时候直接在谷歌浏览器插件上进行调试的，所以如果报错了要尽可能地给到相关地错误信息 -->
### 第三阶段：优化完善
- [ ] 错误处理机制
- [ ] 用户体验优化
- [ ] 测试和调试

### 第四阶段：扩展功能
- [ ] 多网站适配
- [ ] 数据导入导出
- [ ] 高级配置选项

## 技术特点

- **轻量级**: 纯JavaScript实现，无外部依赖
- **安全性**: 本地存储配置，不上传数据
- **兼容性**: 支持Chrome及基于Chromium的浏览器
- **可扩展**: 模块化设计，易于添加新功能

## 注意事项

1. **权限说明**: 插件需要访问当前标签页权限来操作表单
2. **数据安全**: 所有配置数据仅存储在本地
3. **网站兼容**: 不同网站的表单结构可能需要适配
4. **使用限制**: 仅用于测试目的，请遵守网站使用条款

## 🆕 阶段三新功能

### 性能监控与优化
- **操作性能追踪**：记录每个操作的执行时间和性能指标
- **慢操作检测**：自动识别和警告耗时超过1秒的操作
- **性能报告**：提供详细的性能分析报告

### 错误处理与恢复
- **统一错误处理**：集中管理和记录所有错误信息
- **自动重试机制**：支持指数退避的智能重试策略
- **错误日志记录**：本地保存错误日志用于调试分析
- **用户友好提示**：清晰的错误信息和解决建议

### 用户体验增强
- **填充成功率显示**：显示填充成功的字段比例
- **字段类型统计**：展示识别到的字段类型分布
- **操作确认机制**：可选的填充前确认功能
- **实时状态反馈**：详细的操作进度和结果提示

### 数据分析与统计
- **使用统计增强**：记录成功率、错误次数等详细数据
- **性能指标收集**：自动收集和分析插件性能数据
- **调试信息完善**：提供丰富的调试信息和日志

### 字段验证与安全
- **字段格式验证**：支持电话、邮箱、日期等格式验证
- **必填字段检查**：确保重要字段不被遗漏
- **安全操作检查**：验证元素状态和可操作性
- **错误恢复机制**：处理DOM变化和元素失效情况

## 更新日志

### v1.0.0 (当前版本) - 阶段三完整版
- ✅ 基础表单识别和填充功能
- ✅ 支持多种字段类型和智能识别
- ✅ 可配置的填充规则和模板系统
- ✅ 用户友好的界面设计
- 🆕 性能监控和优化系统
- 🆕 完善的错误处理和恢复机制
- 🆕 增强的用户体验和反馈
- 🆕 数据分析和统计功能
- 🆕 字段验证和安全检查
- 🆕 自动重试和智能恢复
- 🆕 详细的调试和日志系统

## 故障排除

### 常见问题

#### 问题：点击插件图标后没有显示按钮
**可能原因**：
- JavaScript加载错误
- CSS样式冲突
- 插件权限问题

**解决步骤**：
1. 重新加载插件（chrome://extensions/）
2. 检查开发者工具中的错误信息
3. 使用调试模式排查问题

#### 问题：表单填充不生效
**可能原因**：
- 页面字段识别失败
- 网站使用了特殊的表单控件
- 权限不足

**解决步骤**：
1. 点击"预览识别"查看识别到的字段
2. 检查网站是否使用iframe
3. 尝试手动刷新页面后重试

#### 问题：CSP (内容安全策略) 错误
**错误信息**：
- `Refused to execute inline script because it violates the following Content Security Policy directive`
- `script-src 'self'`

**原因**：Chrome扩展默认禁止内联脚本执行，这是安全机制

**解决方案**：
1. 我们已经修复了此问题，将内联脚本移到外部文件
2. 如果仍遇到此错误，请确保使用最新版本的文件
3. 重新加载插件后测试

### 调试模式

项目包含调试工具帮助排查问题：

- `popup-debug.js` - 简化的调试脚本
- `popup-test.html` - 最简测试弹窗
- `DEBUG_GUIDE.md` - 详细调试指南

**启用调试模式**：
1. 修改 `popup.html` 引用调试脚本
2. 重新加载插件
3. 查看控制台日志信息

### 技术支持

如遇到问题，请提供：
- 具体错误信息
- 浏览器版本
- 操作系统信息
- 开发者工具截图

---

**开发状态**: 🚧 开发中  
**最后更新**: 2024年12月  
**开发者**: AI助手