<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单自动填充</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            width: 380px;
            height: 500px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            color: #333;
        }
        
        .container {
            width: 100%;
            height: 100%;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h2 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 12px;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 20px;
            flex: 1;
            overflow-y: auto;
        }
        
        .section {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .form-control {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: #fff;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        
        .status {
            background: #f8f9fa;
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .status-value {
            font-weight: 600;
            color: #28a745;
        }
        
        .status-success {
            color: #28a745 !important;
        }
        
        .status-error {
            color: #dc3545 !important;
        }
        
        .status-warning {
            color: #ffc107 !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h2>🚀 表单自动填充</h2>
            <p class="subtitle">让测试更高效</p>
        </div>

        <!-- 主要操作区域 -->
        <div class="main-content">
            <!-- 模板选择 -->
            <div class="section">
                <label for="template-select">选择模板:</label>
                <select id="template-select" class="form-control">
                    <option value="default">默认模板</option>
                    <option value="restaurant">餐饮模板</option>
                    <option value="retail">零售模板</option>
                </select>
            </div>

            <!-- 自定义文本 -->
            <div class="section">
                <label for="brand-scope">品牌经营范围:</label>
                <input type="text" id="brand-scope" class="form-control" 
                       placeholder="例如: 测试" value="测试">
            </div>

            <!-- 操作按钮 -->
            <div class="button-group">
                <button id="fill-form" class="btn btn-primary">
                    🎯 一键填充
                </button>
                <button id="preview-form" class="btn btn-secondary">
                    👀 预览识别
                </button>
            </div>
        </div>

        <!-- 状态显示 -->
        <div class="status" id="status">
            <div class="status-item">
                <span class="status-label">状态:</span>
                <span class="status-value" id="status-text">就绪</span>
            </div>
            <div class="status-item">
                <span class="status-label">识别字段:</span>
                <span class="status-value" id="field-count">0</span>
            </div>
        </div>
    </div>

    <script src="popup-working.js"></script>
</body>
</html>
