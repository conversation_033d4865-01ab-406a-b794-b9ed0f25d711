<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单自动填充插件测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            max-width: 900px;
            margin: 20px auto;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007cba;
        }
        
        .form-section h3 {
            margin: 0 0 20px 0;
            color: #007cba;
            font-size: 18px;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
            transition: all 0.3s ease;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #007cba;
            box-shadow: 0 0 0 3px rgba(0,124,186,0.1);
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-top: 8px;
        }
        
        .radio-group label {
            display: flex;
            align-items: center;
            font-weight: normal;
            margin-bottom: 0;
        }
        
        .radio-group input[type="radio"] {
            width: auto;
            margin-right: 8px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .submit-btn {
            background-color: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background-color: #005a87;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #007cba;
            border-bottom: 2px solid #007cba;
            padding-bottom: 5px;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <h1>表单自动填充插件测试页面</h1>
    
    <div class="instructions">
        <h3>使用说明：</h3>
        <ol>
            <li>确保已安装表单自动填充插件</li>
            <li>点击浏览器工具栏中的插件图标</li>
            <li>在弹出窗口中点击"识别字段"按钮</li>
            <li>点击"填充表单"按钮自动填充下方表单</li>
        </ol>
    </div>

    <div class="form-container">
        <h2>商户入驻申请表单</h2>
        <form id="testForm">
            <!-- 基本信息 -->
            <div class="form-section">
                <h3>基本信息</h3>
                
                <div class="form-group">
                    <label for="companyName">公司名称 *</label>
                    <input type="text" id="companyName" name="companyName" required>
                </div>
                
                <div class="form-group">
                    <label for="contactPerson">联系人 *</label>
                    <input type="text" id="contactPerson" name="contactPerson" required>
                </div>
                
                <div class="form-group">
                    <label for="phone">联系电话 *</label>
                    <input type="tel" id="phone" name="phone" required>
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" name="email">
                </div>
                
                <!-- 签约品牌经营范围 -->
                <div class="form-group">
                    <label for="brandScope">签约品牌经营范围 *</label>
                    <input type="text" id="brandScope" name="签约品牌经营范围" placeholder="请输入经营范围" required>
                </div>
            </div>

            <!-- 商户类型和下拉框字段 -->
            <div class="form-section">
                <h3>商户类型信息</h3>
                
                <div class="form-group">
                    <label for="shopType">商铺类型 *</label>
                    <select id="shopType" name="商铺类型" required>
                        <option value="">请选择商铺类型</option>
                        <option value="flagship">旗舰店</option>
                        <option value="standard">标准店</option>
                        <option value="mini">迷你店</option>
                        <option value="popup">快闪店</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="supplierType">供应商类型 *</label>
                    <select id="supplierType" name="供应商类型" required>
                        <option value="">请选择供应商类型</option>
                        <option value="direct">直营供应商</option>
                        <option value="franchise">加盟供应商</option>
                        <option value="agent">代理供应商</option>
                        <option value="partner">合作供应商</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="contractEntity">签约主体 *</label>
                    <select id="contractEntity" name="签约主体" required>
                        <option value="">请选择签约主体</option>
                        <option value="company">公司总部</option>
                        <option value="branch">分公司</option>
                        <option value="subsidiary">子公司</option>
                        <option value="individual">个人</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="businessMode">经营模式 *</label>
                    <select id="businessMode" name="经营模式" required>
                        <option value="">请选择经营模式</option>
                        <option value="self">自营</option>
                        <option value="franchise">加盟</option>
                        <option value="cooperation">合作</option>
                        <option value="consignment">代销</option>
                    </select>
                </div>
            </div>

            <!-- 数字选择字段 -->
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="has-permit" name="hasPermit">
                    <label for="has-permit">是否有经营许可证</label>
                </div>
            </div>

            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="tax-registered" name="taxRegistered">
                    <label for="tax-registered">是否已税务登记</label>
                </div>
            </div>

            <!-- 时间信息 -->
            <div class="form-section">
                <h3>重要日期</h3>
                
                <div class="form-group">
                    <label for="leaseStartDate">租赁起始时间 *</label>
                    <input type="date" id="leaseStartDate" name="租赁起始时间" required>
                </div>
                
                <div class="form-group">
                    <label for="leaseEndDate">租赁截止时间 *</label>
                    <input type="date" id="leaseEndDate" name="租赁截止时间" required>
                </div>
                
                <div class="form-group">
                    <label for="deliveryDate">商户交付日期 *</label>
                    <input type="date" id="deliveryDate" name="商户交付日期" required>
                </div>
                
                <div class="form-group">
                    <label for="rentStartDate">商户计租日期 *</label>
                    <input type="date" id="rentStartDate" name="商户计租日期" required>
                </div>
                
                <div class="form-group">
                    <label for="openingDate">商户开业日期 *</label>
                    <input type="date" id="openingDate" name="商户开业日期" required>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-group">
                <label for="contact-phone">联系电话：</label>
                <input type="tel" id="contact-phone" name="contactPhone" placeholder="请输入手机号码">
            </div>

            <div class="form-group">
                <label for="contact-email">联系邮箱：</label>
                <input type="email" id="contact-email" name="contactEmail" placeholder="请输入邮箱地址">
            </div>

            <!-- 租赁信息 -->
            <div class="form-section">
                <h3>租赁信息</h3>
                
                <div class="form-group">
                    <label for="leaseType">租赁类型 *</label>
                    <select id="leaseType" name="租赁类型" required>
                        <option value="">请选择租赁类型</option>
                        <option value="long-term">长期租赁</option>
                        <option value="short-term">短期租赁</option>
                        <option value="seasonal">季节性租赁</option>
                        <option value="flexible">灵活租赁</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="rentIncrease">租金递增方式 *</label>
                    <select id="rentIncrease" name="租金递增方式" required>
                        <option value="">请选择递增方式</option>
                        <option value="fixed">固定递增</option>
                        <option value="percentage">百分比递增</option>
                        <option value="market">市场价递增</option>
                        <option value="none">不递增</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="operationFeeIncrease">月运营管理费总额递增方式 *</label>
                    <select id="operationFeeIncrease" name="月运营管理费总额递增方式" required>
                        <option value="">请选择递增方式</option>
                        <option value="fixed">固定递增</option>
                        <option value="percentage">百分比递增</option>
                        <option value="cpi">CPI递增</option>
                        <option value="none">不递增</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="propertyFeeIncrease">月物业管理费总额递增方式 *</label>
                    <select id="propertyFeeIncrease" name="月物业管理费总额递增方式" required>
                        <option value="">请选择递增方式</option>
                        <option value="fixed">固定递增</option>
                        <option value="percentage">百分比递增</option>
                        <option value="cpi">CPI递增</option>
                        <option value="none">不递增</option>
                    </select>
                </div>
            </div>
            
            <!-- 装修信息 -->
            <div class="form-section">
                <h3>装修信息</h3>
                
                <div class="form-group">
                    <label>是否重新装修 *</label>
                    <div class="radio-group">
                        <label>
                            <input type="radio" name="是否重新装修" value="1" required>
                            是
                        </label>
                        <label>
                            <input type="radio" name="是否重新装修" value="0" required>
                            否
                        </label>
                    </div>
                </div>
            </div>

            <!-- 备注信息 -->
            <div class="form-group">
                <label for="remarks">备注信息：</label>
                <textarea id="remarks" name="remarks" rows="4" placeholder="请输入备注信息"></textarea>
            </div>

            <button type="submit" class="submit-btn">提交申请</button>
        </form>
    </div>

    <div class="form-container">
        <h2>测试结果显示</h2>
        <div id="test-results">
            <p>填充完成后，这里将显示表单数据...</p>
        </div>
    </div>

    <script>
        // 表单提交处理
        document.getElementById('merchant-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const results = {};
            
            for (let [key, value] of formData.entries()) {
                results[key] = value;
            }
            
            // 处理复选框
            const checkboxes = this.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                results[checkbox.name] = checkbox.checked;
            });
            
            // 显示结果
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>表单数据：</h3><pre>' + JSON.stringify(results, null, 2) + '</pre>';
            
            alert('表单提交成功！请查看下方的测试结果。');
        });
        
        // 页面加载完成提示
        window.addEventListener('load', function() {
            console.log('测试页面加载完成，可以开始测试插件功能');
        });
    </script>
</body>
</html>