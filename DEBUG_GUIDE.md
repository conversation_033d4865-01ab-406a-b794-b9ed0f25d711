# 插件调试指南

## 问题描述
用户反映点击插件图标后，弹窗中没有显示按钮，需要排查具体原因。

## 调试步骤

### 第一步：重新加载插件
1. 打开Chrome浏览器
2. 在地址栏输入：`chrome://extensions/`
3. 找到"表单自动填充助手"插件
4. 点击右下角的"重新加载"按钮（🔄）
5. 确保插件状态为"已启用"

### 第二步：测试调试版本
1. 点击插件图标（现在标题应该显示"表单自动填充 (调试模式)"）
2. 查看是否能看到按钮
3. 点击"🎯 一键填充"按钮，看是否弹出提示

### 第三步：检查开发者工具
1. 右键点击插件图标
2. 选择"检查弹出式窗口"或"审查元素"
3. 在开发者工具中查看：
   - **Console标签页**：查看是否有错误信息
   - **Elements标签页**：检查HTML结构是否完整
   - **Network标签页**：检查文件是否正常加载

### 第四步：使用最简测试版本
如果调试版本仍有问题，可以使用最简测试版本：

1. 修改 `manifest.json` 文件中的这一行：
   ```json
   "default_popup": "popup/popup-test.html",
   ```

2. 重新加载插件
3. 点击插件图标测试

## 常见问题及解决方案

### 问题1：插件图标是灰色的
**原因**：插件未正确加载或被禁用
**解决**：
- 检查插件是否启用
- 重新加载插件
- 检查manifest.json语法是否正确

### 问题2：点击图标没有反应
**原因**：popup文件路径错误或文件损坏
**解决**：
- 检查文件路径是否正确
- 确认popup.html文件存在
- 使用测试版本验证

### 问题3：弹窗显示但按钮不可见
**原因**：CSS样式问题或JavaScript错误
**解决**：
- 检查popup.css是否正确加载
- 查看开发者工具中的错误信息
- 使用调试版本排查

### 问题4：按钮可见但点击无效
**原因**：JavaScript事件绑定失败
**解决**：
- 检查popup.js中的事件监听器
- 查看控制台错误信息
- 使用调试版本测试

## 调试文件说明

### popup-debug.js
- 简化版的JavaScript文件
- 包含详细的控制台日志
- 基本的按钮点击测试

### popup-test.html
- 最简单的测试弹窗
- 独立的CSS和JavaScript
- 用于验证基本功能

## 下一步操作

根据调试结果：

1. **如果测试版本正常工作**：
   - 问题在于原始JavaScript代码
   - 需要逐步恢复功能
   - 检查popup.js中的错误

2. **如果测试版本也不工作**：
   - 问题在于插件基础配置
   - 检查manifest.json
   - 检查文件权限和路径

3. **如果完全无法显示弹窗**：
   - 插件安装或配置问题
   - 重新安装插件
   - 检查Chrome版本兼容性

## 恢复原始功能

调试完成后，恢复原始设置：

1. 修改 `popup.html` 最后一行：
   ```html
   <script src="popup.js"></script>
   ```

2. 修改 `manifest.json` 中的标题：
   ```json
   "default_title": "表单自动填充",
   ```

3. 重新加载插件

## 联系支持

如果按照以上步骤仍无法解决问题，请提供：
- 具体的错误信息
- 开发者工具截图
- Chrome版本信息
- 操作系统信息