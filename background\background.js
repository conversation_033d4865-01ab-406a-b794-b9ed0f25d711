/**
 * 表单自动填充插件 - 后台服务脚本
 * 负责扩展的生命周期管理和全局事件处理
 */

class BackgroundService {
    constructor() {
        this.init();
    }

    /**
     * 初始化后台服务
     */
    init() {
        // 监听扩展安装事件
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstalled(details);
        });

        // 监听扩展启动事件
        chrome.runtime.onStartup.addListener(() => {
            this.handleStartup();
        });

        // 监听标签页更新事件
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdated(tabId, changeInfo, tab);
        });

        // 监听来自内容脚本和弹窗的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听存储变化
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChanged(changes, namespace);
        });

        console.log('表单自动填充插件后台服务已启动');
    }

    /**
     * 处理扩展安装事件
     */
    async handleInstalled(details) {
        console.log('扩展安装事件:', details);

        if (details.reason === 'install') {
            // 首次安装
            await this.initializeDefaultSettings();
            console.log('扩展首次安装完成');
        } else if (details.reason === 'update') {
            // 扩展更新
            await this.handleExtensionUpdate(details.previousVersion);
            console.log(`扩展从版本 ${details.previousVersion} 更新完成`);
        }
    }

    /**
     * 处理扩展启动事件
     */
    handleStartup() {
        console.log('扩展启动');
        // 可以在这里执行启动时的初始化操作
    }

    /**
     * 处理标签页更新事件
     */
    handleTabUpdated(tabId, changeInfo, tab) {
        // 当页面加载完成时，可以执行一些初始化操作
        if (changeInfo.status === 'complete' && tab.url) {
            // 检查是否为支持的页面
            if (this.isSupportedPage(tab.url)) {
                console.log(`页面加载完成: ${tab.url}`);
                // 可以在这里注入额外的脚本或执行其他操作
            }
        }
    }

    /**
     * 处理消息
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            const { action, data } = message;

            switch (action) {
                case 'getConfig':
                    const config = await this.getStoredConfig();
                    sendResponse({ success: true, config });
                    break;

                case 'saveConfig':
                    await this.saveConfig(data.config);
                    sendResponse({ success: true });
                    break;

                case 'resetConfig':
                    await this.resetToDefaultConfig();
                    sendResponse({ success: true });
                    break;

                case 'getStats':
                    const stats = await this.getUsageStats();
                    sendResponse({ success: true, stats });
                    break;

                case 'logUsage':
                    await this.logUsage(data);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * 处理存储变化
     */
    handleStorageChanged(changes, namespace) {
        console.log('存储变化:', changes, namespace);
        
        // 如果配置发生变化，可以通知所有相关的标签页
        if (changes.formConfig) {
            this.notifyConfigChange(changes.formConfig.newValue);
        }
    }

    /**
     * 初始化默认设置
     */
    async initializeDefaultSettings() {
        const defaultConfig = {
            template: 'default',
            autoDropdown: true,
            autoDate: true,
            autoCheckbox: true,
            brandScope: '餐饮服务',
            dateOffsets: {
                delivery: 7,
                rent: 14,
                opening: 30
            },
            fieldPriority: {
                id: true,
                name: true,
                label: true,
                placeholder: false
            },
            version: '1.0.0',
            installDate: new Date().toISOString()
        };

        try {
            await chrome.storage.local.set({ 
                formConfig: defaultConfig,
                usageStats: {
                    totalFills: 0,
                    totalFields: 0,
                    lastUsed: null,
                    installDate: new Date().toISOString()
                }
            });
            console.log('默认设置初始化完成');
        } catch (error) {
            console.error('初始化默认设置失败:', error);
        }
    }

    /**
     * 处理扩展更新
     */
    async handleExtensionUpdate(previousVersion) {
        try {
            // 获取当前配置
            const result = await chrome.storage.local.get(['formConfig']);
            const currentConfig = result.formConfig || {};

            // 更新版本信息
            currentConfig.version = '1.0.0';
            currentConfig.updateDate = new Date().toISOString();
            currentConfig.previousVersion = previousVersion;

            // 保存更新后的配置
            await chrome.storage.local.set({ formConfig: currentConfig });
            
            console.log('扩展更新处理完成');
        } catch (error) {
            console.error('处理扩展更新失败:', error);
        }
    }

    /**
     * 检查是否为支持的页面
     */
    isSupportedPage(url) {
        // 检查URL是否有效
        if (!url || typeof url !== 'string') {
            return false;
        }
        
        // 排除扩展页面和特殊协议
        const unsupportedProtocols = ['chrome:', 'chrome-extension:', 'moz-extension:', 'edge:'];
        return !unsupportedProtocols.some(protocol => url.startsWith(protocol));
    }

    /**
     * 获取存储的配置
     */
    async getStoredConfig() {
        try {
            const result = await chrome.storage.local.get(['formConfig']);
            return result.formConfig || this.getDefaultConfig();
        } catch (error) {
            console.error('获取配置失败:', error);
            return this.getDefaultConfig();
        }
    }

    /**
     * 保存配置
     */
    async saveConfig(config) {
        try {
            config.lastModified = new Date().toISOString();
            await chrome.storage.local.set({ formConfig: config });
            console.log('配置保存成功');
        } catch (error) {
            console.error('保存配置失败:', error);
            throw error;
        }
    }

    /**
     * 重置为默认配置
     */
    async resetToDefaultConfig() {
        try {
            const defaultConfig = this.getDefaultConfig();
            await chrome.storage.local.set({ formConfig: defaultConfig });
            console.log('配置重置成功');
        } catch (error) {
            console.error('重置配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取默认配置
     */
    getDefaultConfig() {
        return {
            template: 'default',
            autoDropdown: true,
            autoDate: true,
            autoCheckbox: true,
            brandScope: '餐饮服务',
            dateOffsets: {
                delivery: 7,
                rent: 14,
                opening: 30
            },
            fieldPriority: {
                id: true,
                name: true,
                label: true,
                placeholder: false
            },
            version: '1.0.0'
        };
    }

    /**
     * 获取使用统计
     */
    async getUsageStats() {
        try {
            const result = await chrome.storage.local.get(['usageStats']);
            return result.usageStats || {
                totalFills: 0,
                totalFields: 0,
                lastUsed: null,
                installDate: new Date().toISOString()
            };
        } catch (error) {
            console.error('获取使用统计失败:', error);
            return null;
        }
    }

    /**
     * 记录使用情况
     */
    async logUsage(data) {
        try {
            const stats = await this.getUsageStats();
            
            stats.totalFills += 1;
            stats.totalFields += data.fieldCount || 0;
            stats.lastUsed = new Date().toISOString();
            
            // 记录页面URL（不包含敏感信息）
            if (data.url) {
                const url = new URL(data.url);
                stats.lastDomain = url.hostname;
            }
            
            await chrome.storage.local.set({ usageStats: stats });
            console.log('使用情况记录成功');
        } catch (error) {
            console.error('记录使用情况失败:', error);
        }
    }

    /**
     * 通知配置变化
     */
    async notifyConfigChange(newConfig) {
        try {
            // 获取所有标签页
            const tabs = await chrome.tabs.query({});
            
            // 向所有支持的标签页发送配置更新消息
            for (const tab of tabs) {
                if (this.isSupportedPage(tab.url)) {
                    try {
                        await chrome.tabs.sendMessage(tab.id, {
                            action: 'configUpdated',
                            config: newConfig
                        });
                    } catch (error) {
                        // 忽略无法发送消息的标签页（可能没有内容脚本）
                        console.debug(`无法向标签页 ${tab.id} 发送消息:`, error.message);
                    }
                }
            }
        } catch (error) {
            console.error('通知配置变化失败:', error);
        }
    }

    /**
     * 清理过期数据
     */
    async cleanupExpiredData() {
        try {
            // 这里可以实现清理逻辑，比如清理过期的统计数据
            console.log('数据清理完成');
        } catch (error) {
            console.error('数据清理失败:', error);
        }
    }
}

// 初始化后台服务
const backgroundService = new BackgroundService();

// 定期清理数据（每24小时）
setInterval(() => {
    backgroundService.cleanupExpiredData();
}, 24 * 60 * 60 * 1000);

// 错误处理
self.addEventListener('error', (event) => {
    console.error('后台脚本错误:', event.error);
});

// 未处理的Promise拒绝
self.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});