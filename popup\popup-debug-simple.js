console.log('=== 调试脚本开始加载 ===');

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM 加载完成');
    
    const status = document.getElementById('status');
    const btn1 = document.getElementById('test-btn-1');
    const btn2 = document.getElementById('test-btn-2');
    
    // 更新状态
    status.textContent = '状态：已就绪';
    
    // 按钮1事件
    if (btn1) {
        btn1.addEventListener('click', function() {
            console.log('按钮1被点击');
            status.textContent = '状态：按钮1被点击！';
            alert('按钮1工作正常！\n\n这说明插件基本功能正常。');
        });
        console.log('按钮1事件已绑定');
    }
    
    // 按钮2事件
    if (btn2) {
        btn2.addEventListener('click', function() {
            console.log('按钮2被点击');
            status.textContent = '状态：按钮2被点击！';
            alert('按钮2工作正常！\n\n插件界面显示正常。');
        });
        console.log('按钮2事件已绑定');
    }
    
    console.log('所有事件绑定完成');
});

// 显示窗口尺寸信息
window.addEventListener('load', function() {
    console.log('窗口尺寸:', window.innerWidth + 'x' + window.innerHeight);
    console.log('body尺寸:', document.body.offsetWidth + 'x' + document.body.offsetHeight);
});

console.log('=== 调试脚本加载完成 ===');
