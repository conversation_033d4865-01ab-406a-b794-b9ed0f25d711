{
  "version": "1.0.0",
  "description": "表单自动填充配置文件",
  "lastUpdated": "2024-12-19",
  
  "templates": {
    "default": {
      "name": "默认模板",
      "description": "适用于大多数表单的通用配置",
      "fields": {
        "brandScope": "测试",
        "dropdowns": {
          "selectFirst": true,
          "skipEmpty": true
        },
        "checkboxes": {
          "renovation": true,
          "defaultValue": true
        },
        "dates": {
          "useCurrentDate": ["leaseStart", "leaseEnd"],
          "customOffsets": {
            "deliveryDate": 7,
            "rentStartDate": 14,
            "openingDate": 30
          }
        }
      }
    },
    
    "restaurant": {
      "name": "餐饮模板",
      "description": "专门用于餐饮行业的表单配置",
      "fields": {
        "brandScope": "测试",
        "dropdowns": {
          "selectFirst": true,
          "preferredValues": {
            "shopType": "餐饮店",
            "businessMode": "直营"
          }
        },
        "checkboxes": {
          "renovation": true
        },
        "dates": {
          "useCurrentDate": ["leaseStart"],
          "customOffsets": {
            "deliveryDate": 10,
            "rentStartDate": 20,
            "openingDate": 45
          }
        }
      }
    },
    
    "retail": {
      "name": "零售模板",
      "description": "适用于零售商铺的表单配置",
      "fields": {
        "brandScope": "测试",
        "dropdowns": {
          "selectFirst": true,
          "preferredValues": {
            "shopType": "零售店",
            "businessMode": "加盟"
          }
        },
        "checkboxes": {
          "renovation": false
        },
        "dates": {
          "useCurrentDate": ["leaseStart", "leaseEnd"],
          "customOffsets": {
            "deliveryDate": 5,
            "rentStartDate": 10,
            "openingDate": 21
          }
        }
      }
    }
  },
  
  "fieldMappings": {
    "brandScope": {
      "selectors": [
        "input[name*='经营范围']",
        "input[name*='brand']",
        "input[name*='scope']",
        "textarea[name*='经营范围']",
        "input[placeholder*='经营范围']",
        "input[id*='business-scope']",
        "input[class*='scope']"
      ],
      "keywords": ["经营范围", "业务范围", "经营内容", "business scope"],
      "defaultValue": "测试"
    },
    
    "dropdowns": {
      "shopType": {
        "selectors": [
          "select[name*='商铺类型']",
          "select[name*='shop']",
          "select[name*='store']",
          "select[id*='shop-type']"
        ],
        "keywords": ["商铺类型", "店铺类型", "shop type"],
        "preferredValues": ["餐饮店", "零售店", "服务店"]
      },
      
      "supplierType": {
        "selectors": [
          "select[name*='供应商类型']",
          "select[name*='supplier']",
          "select[id*='supplier-type']"
        ],
        "keywords": ["供应商类型", "supplier type"],
        "preferredValues": ["一级供应商", "二级供应商"]
      },
      
      "contractEntity": {
        "selectors": [
          "select[name*='签约主体']",
          "select[name*='contract']",
          "select[name*='entity']",
          "select[id*='contract-entity']"
        ],
        "keywords": ["签约主体", "合同主体", "contract entity"],
        "preferredValues": ["公司", "个人"]
      },
      
      "businessMode": {
        "selectors": [
          "select[name*='经营模式']",
          "select[name*='business']",
          "select[name*='mode']",
          "select[id*='business-mode']"
        ],
        "keywords": ["经营模式", "business mode"],
        "preferredValues": ["直营", "加盟", "代理"]
      },
      
      "leaseType": {
        "selectors": [
          "select[name*='租赁类型']",
          "select[name*='lease']",
          "select[name*='rent']",
          "select[id*='lease-type']"
        ],
        "keywords": ["租赁类型", "lease type"],
        "preferredValues": ["长期租赁", "短期租赁"]
      },
      
      "rentIncrease": {
        "selectors": [
          "select[name*='租金递增']",
          "select[name*='rent'][name*='increase']",
          "select[id*='rent-increase']"
        ],
        "keywords": ["租金递增", "rent increase"],
        "preferredValues": ["固定递增", "按比例递增", "不递增"]
      },
      
      "operationFeeIncrease": {
        "selectors": [
          "select[name*='运营管理费'][name*='递增']",
          "select[name*='operation'][name*='fee']",
          "select[id*='operation-fee-increase']"
        ],
        "keywords": ["运营管理费递增", "operation fee increase"],
        "preferredValues": ["固定递增", "按比例递增", "不递增"]
      },
      
      "propertyFeeIncrease": {
        "selectors": [
          "select[name*='物业管理费'][name*='递增']",
          "select[name*='property'][name*='fee']",
          "select[id*='property-fee-increase']"
        ],
        "keywords": ["物业管理费递增", "property fee increase"],
        "preferredValues": ["固定递增", "按比例递增", "不递增"]
      }
    },
    
    "checkboxes": {
      "renovation": {
        "selectors": [
          "input[type='checkbox'][name*='装修']",
          "input[type='radio'][name*='装修']",
          "input[name*='renovation']",
          "input[id*='renovation']"
        ],
        "keywords": ["是否重新装修", "装修", "renovation"],
        "defaultValue": true,
        "yesValues": ["是", "1", "true", "yes", "Y"]
      }
    },
    
    "dates": {
      "leaseStart": {
        "selectors": [
          "input[type='date'][name*='租赁'][name*='开始']",
          "input[type='date'][name*='lease'][name*='start']",
          "input[name*='租赁起始']",
          "input[id*='lease-start']"
        ],
        "keywords": ["租赁开始日期", "lease start date"],
        "useCurrentDate": true
      },
      
      "leaseEnd": {
        "selectors": [
          "input[type='date'][name*='租赁'][name*='结束']",
          "input[type='date'][name*='lease'][name*='end']",
          "input[name*='租赁截止']",
          "input[id*='lease-end']"
        ],
        "keywords": ["租赁结束日期", "lease end date"],
        "useCurrentDate": true
      },
      
      "deliveryDate": {
        "selectors": [
          "input[type='date'][name*='交付']",
          "input[name*='delivery']",
          "input[name*='商户交付']",
          "input[id*='delivery-date']"
        ],
        "keywords": ["商户交付日期", "delivery date"],
        "offsetDays": 7
      },
      
      "rentStartDate": {
        "selectors": [
          "input[type='date'][name*='计租']",
          "input[name*='rent'][name*='start']",
          "input[name*='商户计租']",
          "input[id*='rent-start']"
        ],
        "keywords": ["商户计租日期", "rent start date"],
        "offsetDays": 14
      },
      
      "openingDate": {
        "selectors": [
          "input[type='date'][name*='开业']",
          "input[name*='opening']",
          "input[name*='商户开业']",
          "input[id*='opening-date']"
        ],
        "keywords": ["商户开业日期", "opening date"],
        "offsetDays": 30
      }
    }
  },
  
  "commonValues": {
    "textFields": {
      "name": "测试用户",
      "company": "测试公司",
      "phone": "13800138000",
      "email": "<EMAIL>",
      "address": "测试地址",
      "contact": "张三",
      "title": "经理",
      "department": "运营部"
    },
    
    "numberFields": {
      "area": "100",
      "rent": "5000",
      "deposit": "10000",
      "managementFee": "500"
    }
  },
  
  "settings": {
    "fillDelay": 100,
    "maxRetries": 3,
    "enableLogging": true,
    "autoSave": true,
    "confirmBeforeFill": false,
    "highlightFields": true,
    "skipReadonly": true,
    "skipHidden": true
  },
  
  "validation": {
    "requiredFields": [
      "brandScope"
    ],
    "fieldTypes": {
      "phone": "^1[3-9]\\d{9}$",
      "email": "^[\\w-\.]+@([\\w-]+\.)+[\\w-]{2,4}$",
      "date": "^\\d{4}-\\d{2}-\\d{2}$"
    }
  }
}