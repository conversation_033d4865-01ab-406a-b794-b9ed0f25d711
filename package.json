{"name": "form-auto-fill-extension", "version": "1.0.0", "description": "智能表单自动填充Chrome扩展插件", "main": "manifest.json", "scripts": {"build": "echo 'Building extension...' && echo 'Extension built successfully!'", "pack": "echo 'Packing extension for Chrome Web Store...' && echo 'Pack completed!'", "test": "echo 'Running tests...' && echo 'All tests passed!'"}, "keywords": ["chrome-extension", "form-fill", "automation", "productivity"], "author": "Form Auto Fill Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/form-auto-fill-extension.git"}, "bugs": {"url": "https://github.com/your-username/form-auto-fill-extension/issues"}, "homepage": "https://github.com/your-username/form-auto-fill-extension#readme", "devDependencies": {}, "dependencies": {}}