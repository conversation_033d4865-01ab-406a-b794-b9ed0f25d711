/**
 * 表单自动填充插件 - 内容脚本
 * 负责在网页中识别和操作表单字段
 */

class FormAutoFiller {
    constructor() {
        this.fields = new Map();
        this.config = null;
        this.fieldSelectors = this.getFieldSelectors();
        
        this.init();
    }

    /**
     * 初始化内容脚本
     */
    init() {
        // 监听来自弹窗的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });
        
        console.log('表单自动填充插件已加载');
    }

    /**
     * 处理来自弹窗的消息
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            const { action, config } = message;
            this.config = config;
            
            switch (action) {
                case 'ping':
                    sendResponse({ success: true, message: 'Content script is ready' });
                    break;

                case 'previewFields':
                    const fieldCount = await this.identifyFields();
                    sendResponse({ success: true, fieldCount });
                    break;

                case 'fillForm':
                    const filledCount = await this.fillAllFields();
                    sendResponse({ success: true, filledCount });
                    break;

                default:
                    sendResponse({ success: false, error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * 获取字段选择器配置
     */
    getFieldSelectors() {
        return {
            // 签约品牌经营范围 - 支持Element UI和传统表单
            brandScope: [
                // Element UI 输入框
                '.el-input__inner[placeholder*="经营范围"]',
                '.el-input__inner[placeholder*="品牌"]',
                '.el-textarea__inner[placeholder*="经营范围"]',
                // 传统表单
                'input[name*="经营范围"]',
                'input[name*="brand"]',
                'input[name*="scope"]',
                'textarea[name*="经营范围"]',
                'input[placeholder*="经营范围"]',
                'input[id*="business-scope"]',
                'input[class*="scope"]',
                'input[aria-label*="经营范围"]',
                // 通用文本输入框
                'input[type="text"]',
                'textarea'
            ],

            // 下拉框字段 - Element UI Select
            dropdowns: {
                template: [
                    // Element UI Select
                    '.el-select input[placeholder*="模板"]',
                    '.el-select input[placeholder*="选择模板"]',
                    '.el-select input[placeholder*="默认模板"]',
                    'select[name*="template"]'
                ],
                shopType: [
                    '.el-select input[placeholder*="商铺类型"]',
                    '.el-select input[placeholder*="店铺类型"]',
                    'select[name*="商铺类型"]',
                    'select[name*="shop"]',
                    'select[name*="store"]'
                ],
                supplierType: [
                    '.el-select input[placeholder*="供应商类型"]',
                    '.el-select input[placeholder*="供应商"]',
                    'select[name*="供应商类型"]',
                    'select[name*="supplier"]'
                ],
                contractEntity: [
                    '.el-select input[placeholder*="签约主体"]',
                    '.el-select input[placeholder*="主体"]',
                    'select[name*="签约主体"]',
                    'select[name*="contract"]',
                    'select[name*="entity"]'
                ],
                businessMode: [
                    '.el-select input[placeholder*="经营模式"]',
                    '.el-select input[placeholder*="模式"]',
                    'select[name*="经营模式"]',
                    'select[name*="business"]',
                    'select[name*="mode"]'
                ],
                leaseType: [
                    '.el-select input[placeholder*="租赁类型"]',
                    '.el-select input[placeholder*="租赁"]',
                    'select[name*="租赁类型"]',
                    'select[name*="lease"]',
                    'select[name*="rent"]'
                ],
                rentIncrease: [
                    '.el-select input[placeholder*="租金递增"]',
                    '.el-select input[placeholder*="递增"]',
                    'select[name*="租金递增"]',
                    'select[name*="rent"][name*="increase"]'
                ],
                operationFeeIncrease: [
                    '.el-select input[placeholder*="运营管理费"]',
                    '.el-select input[placeholder*="管理费"]',
                    'select[name*="运营管理费"][name*="递增"]',
                    'select[name*="operation"][name*="fee"]'
                ],
                propertyFeeIncrease: [
                    '.el-select input[placeholder*="物业管理费"]',
                    '.el-select input[placeholder*="物业费"]',
                    'select[name*="物业管理费"][name*="递增"]',
                    'select[name*="property"][name*="fee"]'
                ]
            },

            // 复选框/单选框 - Element UI Checkbox/Radio
            checkboxes: {
                renovation: [
                    '.el-checkbox input[type="checkbox"]',
                    '.el-radio input[type="radio"]',
                    'input[type="checkbox"][name*="装修"]',
                    'input[type="radio"][name*="装修"]',
                    'input[name*="renovation"]'
                ],
                autoDropdown: [
                    '.el-checkbox input[type="checkbox"]',
                    'input[type="checkbox"][name*="自动下拉"]',
                    'input[type="checkbox"][name*="dropdown"]'
                ],
                autoDate: [
                    '.el-checkbox input[type="checkbox"]',
                    'input[type="checkbox"][name*="自动日期"]',
                    'input[type="checkbox"][name*="date"]'
                ],
                autoCheckbox: [
                    '.el-checkbox input[type="checkbox"]',
                    'input[type="checkbox"][name*="自动复选框"]',
                    'input[type="checkbox"][name*="checkbox"]'
                ]
            },

            // 日期字段 - Element UI DatePicker
            dates: {
                leaseStart: [
                    '.el-date-editor input[placeholder*="租赁"][placeholder*="开始"]',
                    '.el-date-editor input[placeholder*="开始日期"]',
                    'input[type="date"][name*="租赁"][name*="开始"]',
                    'input[type="date"][name*="lease"][name*="start"]',
                    'input[name*="租赁起始"]'
                ],
                leaseEnd: [
                    '.el-date-editor input[placeholder*="租赁"][placeholder*="结束"]',
                    '.el-date-editor input[placeholder*="结束日期"]',
                    'input[type="date"][name*="租赁"][name*="结束"]',
                    'input[type="date"][name*="lease"][name*="end"]',
                    'input[name*="租赁截止"]'
                ],
                deliveryDate: [
                    '.el-date-editor input[placeholder*="交付"]',
                    '.el-date-editor input[placeholder*="交付日期"]',
                    'input[type="date"][name*="交付"]',
                    'input[name*="delivery"]',
                    'input[name*="商户交付"]'
                ],
                rentStartDate: [
                    '.el-date-editor input[placeholder*="计租"]',
                    '.el-date-editor input[placeholder*="计租日期"]',
                    'input[type="date"][name*="计租"]',
                    'input[name*="rent"][name*="start"]',
                    'input[name*="商户计租"]'
                ],
                openingDate: [
                    '.el-date-editor input[placeholder*="开业"]',
                    '.el-date-editor input[placeholder*="开业日期"]',
                    'input[type="date"][name*="开业"]',
                    'input[name*="opening"]',
                    'input[name*="商户开业"]'
                ]
            }
        };
    }

    /**
     * 识别页面中的表单字段
     */
    async identifyFields() {
        this.fields.clear();
        let totalCount = 0;
        const processedElements = new Set(); // 防止重复处理同一元素

        try {
            console.log('开始识别表单字段...');

            // 首先尝试智能识别所有表单字段
            totalCount += this.identifyAllFormFields(processedElements);

            // 识别品牌经营范围字段
            const brandScopeField = this.findFieldBySelectors(this.fieldSelectors.brandScope);
            if (brandScopeField && !this.fields.has('brandScope') && !processedElements.has(brandScopeField)) {
                this.fields.set('brandScope', {
                    element: brandScopeField,
                    type: 'text',
                    label: this.getFieldLabel(brandScopeField)
                });
                processedElements.add(brandScopeField);
                totalCount++;
                console.log('识别到品牌经营范围字段:', brandScopeField);
            }

            // 识别下拉框字段
            for (const [key, selectors] of Object.entries(this.fieldSelectors.dropdowns)) {
                const field = this.findFieldBySelectors(selectors);
                if (field && !this.fields.has(`dropdown_${key}`) && !processedElements.has(field)) {
                    this.fields.set(`dropdown_${key}`, {
                        element: field,
                        type: 'select',
                        label: this.getFieldLabel(field),
                        options: this.getSelectOptions(field)
                    });
                    processedElements.add(field);
                    totalCount++;
                    console.log(`识别到下拉框字段 ${key}:`, field);
                }
            }

            // 识别复选框字段
            for (const [key, selectors] of Object.entries(this.fieldSelectors.checkboxes)) {
                const field = this.findFieldBySelectors(selectors);
                if (field && !this.fields.has(`checkbox_${key}`) && !processedElements.has(field)) {
                    this.fields.set(`checkbox_${key}`, {
                        element: field,
                        type: field.type,
                        label: this.getFieldLabel(field)
                    });
                    processedElements.add(field);
                    totalCount++;
                    console.log(`识别到复选框字段 ${key}:`, field);
                }
            }

            // 识别日期字段
            for (const [key, selectors] of Object.entries(this.fieldSelectors.dates)) {
                const field = this.findFieldBySelectors(selectors);
                if (field && !processedElements.has(field)) {
                    this.fields.set(`date_${key}`, {
                        element: field,
                        type: 'date',
                        label: this.getFieldLabel(field)
                    });
                    processedElements.add(field);
                    totalCount++;
                    console.log(`识别到日期字段 ${key}:`, field);
                }
            }
            
            // 通用字段识别（作为补充）
            const genericFields = this.findGenericFields();
            genericFields.forEach((field, index) => {
                if (!this.isFieldAlreadyIdentified(field)) {
                    this.fields.set(`generic_${index}`, {
                        element: field,
                        type: this.getFieldType(field),
                        label: this.getFieldLabel(field)
                    });
                    totalCount++;
                }
            });
            
            console.log(`识别到 ${totalCount} 个表单字段:`, this.fields);
            return totalCount;
            
        } catch (error) {
            console.error('识别字段失败:', error);
            return 0;
        }
    }

    /**
     * 智能识别所有表单字段
     */
    identifyAllFormFields(processedElements = new Set()) {
        let count = 0;
        const maxFields = 50; // 限制最大字段数量，防止性能问题

        // 识别所有Element UI输入框
        const elInputs = document.querySelectorAll('.el-input__inner');
        for (let index = 0; index < elInputs.length && count < maxFields; index++) {
            const input = elInputs[index];
            if (!this.isFieldAlreadyIdentified(input) &&
                !processedElements.has(input) &&
                this.isValidFormField(input)) {

                const fieldKey = `el_input_${index}`;
                this.fields.set(fieldKey, {
                    element: input,
                    type: 'text',
                    label: this.getFieldLabel(input),
                    placeholder: input.placeholder || ''
                });
                processedElements.add(input);
                count++;
                console.log(`识别到Element UI输入框 ${index}:`, {
                    element: input,
                    label: this.getFieldLabel(input),
                    placeholder: input.placeholder,
                    hasValue: this.hasValue(input)
                });
            }
        }

        // 识别所有Element UI选择框
        const elSelects = document.querySelectorAll('.el-select input');
        for (let index = 0; index < elSelects.length && count < maxFields; index++) {
            const select = elSelects[index];
            if (!this.isFieldAlreadyIdentified(select) &&
                !processedElements.has(select) &&
                this.isValidFormField(select)) {

                const fieldKey = `el_select_${index}`;
                this.fields.set(fieldKey, {
                    element: select,
                    type: 'select',
                    label: this.getFieldLabel(select),
                    placeholder: select.placeholder || '',
                    options: this.getElementUISelectOptions(select)
                });
                processedElements.add(select);
                count++;
                console.log(`识别到Element UI选择框 ${index}:`, {
                    element: select,
                    label: this.getFieldLabel(select),
                    placeholder: select.placeholder,
                    hasValue: this.hasValue(select)
                });
            }
        }

        // 识别所有Element UI复选框
        const elCheckboxes = document.querySelectorAll('.el-checkbox input[type="checkbox"]');
        for (let index = 0; index < elCheckboxes.length && count < maxFields; index++) {
            const checkbox = elCheckboxes[index];
            if (!this.isFieldAlreadyIdentified(checkbox) &&
                !processedElements.has(checkbox) &&
                this.isValidFormField(checkbox)) {

                const fieldKey = `el_checkbox_${index}`;
                this.fields.set(fieldKey, {
                    element: checkbox,
                    type: 'checkbox',
                    label: this.getFieldLabel(checkbox)
                });
                processedElements.add(checkbox);
                count++;
                console.log(`识别到Element UI复选框 ${index}:`, {
                    element: checkbox,
                    label: this.getFieldLabel(checkbox),
                    checked: checkbox.checked
                });
            }
        }

        // 识别所有Element UI日期选择器
        const elDatePickers = document.querySelectorAll('.el-date-editor input');
        elDatePickers.forEach((datePicker, index) => {
            if (!this.isFieldAlreadyIdentified(datePicker)) {
                const fieldKey = `el_date_${index}`;
                this.fields.set(fieldKey, {
                    element: datePicker,
                    type: 'date',
                    label: this.getFieldLabel(datePicker),
                    placeholder: datePicker.placeholder || ''
                });
                count++;
                console.log(`识别到Element UI日期选择器 ${index}:`, datePicker, '标签:', this.getFieldLabel(datePicker));
            }
        });

        // 识别传统表单元素
        const traditionalInputs = document.querySelectorAll('input:not(.el-input__inner), select:not(.el-select select), textarea:not(.el-textarea__inner)');
        traditionalInputs.forEach((input, index) => {
            if (!this.isFieldAlreadyIdentified(input) && input.type !== 'hidden' && input.type !== 'submit' && input.type !== 'button') {
                const fieldKey = `traditional_${input.type}_${index}`;
                this.fields.set(fieldKey, {
                    element: input,
                    type: input.type || 'text',
                    label: this.getFieldLabel(input),
                    placeholder: input.placeholder || ''
                });
                count++;
                console.log(`识别到传统表单元素 ${index}:`, input, '标签:', this.getFieldLabel(input));
            }
        });

        return count;
    }

    /**
     * 检查字段是否已经被识别
     */
    isFieldAlreadyIdentified(element) {
        for (const [key, field] of this.fields) {
            if (field.element === element) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查字段是否已有值
     */
    hasValue(element) {
        if (!element) return false;

        try {
            // 检查输入框类型
            if (element.tagName.toLowerCase() === 'input') {
                const type = element.type.toLowerCase();

                switch (type) {
                    case 'text':
                    case 'email':
                    case 'tel':
                    case 'url':
                    case 'password':
                        return element.value && element.value.trim() !== '';

                    case 'checkbox':
                    case 'radio':
                        return element.checked;

                    case 'date':
                    case 'datetime-local':
                    case 'time':
                        return element.value && element.value !== '';

                    default:
                        return element.value && element.value.trim() !== '';
                }
            }

            // 检查选择框
            if (element.tagName.toLowerCase() === 'select') {
                return element.selectedIndex > 0 || (element.value && element.value !== '');
            }

            // 检查文本域
            if (element.tagName.toLowerCase() === 'textarea') {
                return element.value && element.value.trim() !== '';
            }

            // Element UI 特殊处理
            if (element.classList.contains('el-input__inner')) {
                return element.value && element.value.trim() !== '';
            }

            // Element UI 选择框 - 检查是否有选中的值
            if (element.closest('.el-select')) {
                const selectWrapper = element.closest('.el-select');
                const selectedText = selectWrapper.querySelector('.el-input__inner');
                return selectedText && selectedText.value && selectedText.value.trim() !== '';
            }

            return false;
        } catch (error) {
            console.warn('检查字段值时出错:', error);
            return false;
        }
    }

    /**
     * 检查是否是有效的表单字段
     */
    isValidFormField(element) {
        if (!element) return false;

        try {
            // 检查元素是否在DOM中
            if (!document.contains(element)) {
                return false;
            }

            // 检查元素是否可见
            const style = window.getComputedStyle(element);
            if (style.display === 'none' || style.visibility === 'hidden') {
                return false;
            }

            // 检查元素是否被禁用
            if (element.disabled || element.readOnly) {
                return false;
            }

            // 检查元素类型
            const tagName = element.tagName.toLowerCase();
            const validTags = ['input', 'select', 'textarea'];

            if (!validTags.includes(tagName)) {
                // 检查是否是Element UI组件
                if (!element.classList.contains('el-input__inner') &&
                    !element.closest('.el-select')) {
                    return false;
                }
            }

            return true;
        } catch (error) {
            console.warn('检查字段有效性时出错:', error);
            return false;
        }
    }

    /**
     * 根据选择器查找字段
     */
    findFieldBySelectors(selectors) {
        for (const selector of selectors) {
            try {
                const element = document.querySelector(selector);
                if (element && this.isValidFormField(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`选择器 "${selector}" 无效:`, error);
            }
        }
        return null;
    }

    /**
     * 查找通用表单字段
     */
    findGenericFields() {
        const fields = [];
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="tel"]',
            'input[type="number"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'select',
            'textarea',
            'input[type="checkbox"]',
            'input[type="radio"]'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (this.isValidFormField(element)) {
                    fields.push(element);
                }
            });
        });
        
        return fields;
    }

    /**
     * 检查是否为有效的表单字段
     */
    isValidFormField(element) {
        // 检查元素是否可见
        if (!this.isElementVisible(element)) {
            return false;
        }
        
        // 检查是否为只读或禁用
        if (element.readOnly || element.disabled) {
            return false;
        }
        
        // 检查是否为隐藏字段
        if (element.type === 'hidden') {
            return false;
        }
        
        return true;
    }

    /**
     * 检查元素是否可见
     */
    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0' &&
               element.offsetWidth > 0 && 
               element.offsetHeight > 0;
    }

    /**
     * 检查字段是否已被识别
     */
    isFieldAlreadyIdentified(element) {
        for (const fieldInfo of this.fields.values()) {
            if (fieldInfo.element === element) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(element) {
        // 尝试多种方式获取字段标签
        let label = '';
        
        // 1. 通过 aria-label 属性
        if (element.getAttribute('aria-label')) {
            label = element.getAttribute('aria-label');
        }
        // 2. 通过关联的 label 元素
        else if (element.id) {
            const labelElement = document.querySelector(`label[for="${element.id}"]`);
            if (labelElement) {
                label = labelElement.textContent.trim();
            }
        }
        // 3. 通过父级 label 元素
        else {
            const parentLabel = element.closest('label');
            if (parentLabel) {
                label = parentLabel.textContent.replace(element.value || '', '').trim();
            }
        }
        // 4. 通过 placeholder 属性
        if (!label && element.placeholder) {
            label = element.placeholder;
        }
        // 5. 通过 name 属性
        if (!label && element.name) {
            label = element.name;
        }
        
        return label || '未知字段';
    }

    /**
     * 获取下拉框选项
     */
    getSelectOptions(selectElement) {
        const options = [];
        if (selectElement.tagName.toLowerCase() === 'select') {
            for (let i = 0; i < selectElement.options.length; i++) {
                const option = selectElement.options[i];
                options.push({
                    value: option.value,
                    text: option.textContent.trim(),
                    index: i
                });
            }
        }
        return options;
    }

    /**
     * 获取Element UI选择框选项
     */
    getElementUISelectOptions(selectInput) {
        const options = [];
        try {
            // 查找关联的下拉选项
            const selectWrapper = selectInput.closest('.el-select');
            if (selectWrapper) {
                // 尝试触发下拉框显示选项
                selectInput.click();

                // 等待选项加载
                setTimeout(() => {
                    const dropdownOptions = document.querySelectorAll('.el-select-dropdown__item');
                    dropdownOptions.forEach((option, index) => {
                        options.push({
                            value: option.textContent.trim(),
                            text: option.textContent.trim(),
                            index: index,
                            element: option
                        });
                    });
                }, 100);
            }
        } catch (error) {
            console.warn('获取Element UI选择框选项失败:', error);
        }
        return options;
    }

    /**
     * 填充所有识别到的字段
     */
    async fillAllFields() {
        let filledCount = 0;
        let skippedCount = 0;
        let errorCount = 0;
        const errors = [];
        const delay = this.config.fillDelay || 50; // 减少延迟
        const maxRetries = this.config.maxRetries || 2; // 减少重试次数

        try {
            console.log(`开始填充表单字段... 共识别到 ${this.fields.size} 个字段`);

            // 过滤有效字段，跳过已有值的字段
            const validFields = new Map();
            for (const [key, fieldInfo] of this.fields) {
                if (this.isValidFormField(fieldInfo.element)) {
                    if (this.hasValue(fieldInfo.element)) {
                        skippedCount++;
                        console.log(`字段 ${key} 已有值，跳过填充`);
                    } else {
                        validFields.set(key, fieldInfo);
                    }
                } else {
                    console.warn(`字段 ${key} 无效，跳过`);
                }
            }

            console.log(`有效字段: ${validFields.size}, 跳过字段: ${skippedCount}`);

            // 分批处理字段，避免一次性处理太多
            const fieldEntries = Array.from(validFields.entries());
            const batchSize = 5; // 每批处理5个字段

            for (let i = 0; i < fieldEntries.length; i += batchSize) {
                const batch = fieldEntries.slice(i, i + batchSize);

                // 并行处理当前批次的字段
                const batchPromises = batch.map(async ([key, fieldInfo]) => {
                    let retryCount = 0;
                    let fieldFilled = false;

                    while (retryCount < maxRetries && !fieldFilled) {
                        try {
                            console.log(`正在填充字段: ${key} (尝试 ${retryCount + 1}/${maxRetries}): ${fieldInfo.label}`);

                            const success = await this.fillField(key, fieldInfo);
                            if (success) {
                                fieldFilled = true;
                                console.log(`成功填充字段 ${key}: ${fieldInfo.label}`);
                                return { success: true, key };
                            } else {
                                console.log(`字段 ${key} 填充失败，可能已有值或不支持`);
                                return { success: false, key, reason: 'fill_failed' };
                            }

                        } catch (error) {
                            retryCount++;
                            console.error(`填充字段 ${key} 时出错 (尝试 ${retryCount}/${maxRetries}):`, error);

                            if (retryCount >= maxRetries) {
                                return {
                                    success: false,
                                    key,
                                    error: error.message || '未知错误',
                                    element: fieldInfo.element?.tagName || 'unknown'
                                };
                            } else {
                                // 重试前等待
                                await this.sleep(delay);
                            }
                        }
                    }

                    return { success: false, key, reason: 'max_retries_exceeded' };
                });

                // 等待当前批次完成
                const batchResults = await Promise.all(batchPromises);

                // 统计结果
                batchResults.forEach(result => {
                    if (result.success) {
                        filledCount++;
                    } else if (result.error) {
                        errorCount++;
                        errors.push({
                            field: result.key,
                            error: result.error,
                            element: result.element
                        });
                    }
                });

                // 批次间延迟
                if (i + batchSize < fieldEntries.length) {
                    await this.sleep(delay * 2);
                }
            }
            
            console.log(`填充完成，成功填充 ${filledCount} 个字段，失败 ${errorCount} 个字段`);
            
            return {
                success: errorCount === 0,
                filledCount,
                errorCount,
                errors: errors.length > 0 ? errors : undefined,
                message: errorCount > 0 ? `部分字段填充失败 (${errorCount}个)` : '所有字段填充成功'
            };
            
        } catch (error) {
            console.error('填充字段失败:', error);
            return {
                success: false,
                error: error.message,
                filledCount,
                errorCount
            };
        }
    }

    /**
     * 填充单个字段
     */
    async fillField(key, fieldInfo) {
        try {
            const { element, type } = fieldInfo;
            
            // 检查字段是否仍然有效
            if (!this.isValidFormField(element)) {
                console.warn(`字段 ${key} 不再有效，跳过填充`);
                return false;
            }
            
            // 高亮显示正在填充的字段
            if (this.config.highlightFields) {
                this.highlightField(element);
            }
            
            let success = false;
            
            switch (type) {
                case 'text':
                case 'textarea':
                    success = this.fillTextField(key, fieldInfo);
                    break;
                    
                case 'select':
                    success = this.fillSelectField(key, fieldInfo);
                    break;
                    
                case 'checkbox':
                case 'radio':
                    success = this.fillCheckboxField(key, fieldInfo);
                    break;
                    
                case 'date':
                    success = this.fillDateField(key, fieldInfo);
                    break;
                    
                default:
                    console.warn(`不支持的字段类型: ${type}`);
                    success = false;
            }
            
            // 移除高亮
            if (this.config.highlightFields) {
                setTimeout(() => this.removeHighlight(element), 1000);
            }
            
            return success;
            
        } catch (error) {
            console.error(`填充字段 ${key} 失败:`, error);
            return false;
        }
    }

    /**
     * 获取字段类型
     */
    getFieldType(field) {
        if (field.tagName.toLowerCase() === 'select') {
            return 'select';
        }
        if (field.tagName.toLowerCase() === 'textarea') {
            return 'textarea';
        }
        if (field.type === 'checkbox' || field.type === 'radio') {
            return field.type;
        }
        if (field.type === 'date' || field.type === 'datetime-local') {
            return 'date';
        }
        return 'text';
    }

    /**
     * 填充文本字段
     */
    fillTextField(key, fieldInfo) {
        const { element } = fieldInfo;

        // 检查字段是否已有值
        if (this.hasValue(element)) {
            console.log(`文本字段 ${key} 已有值，跳过填充`);
            return false;
        }

        let value = '';

        if (key === 'brandScope') {
            value = this.config.brandScope || '测试';
        } else {
            // 根据字段名称或属性推断合适的值
            value = this.inferTextValue(element);
        }

        if (value) {
            // 检查是否是Element UI输入框
            const isElementUI = element.classList.contains('el-input__inner') ||
                               element.classList.contains('el-textarea__inner');

            if (isElementUI) {
                // Element UI输入框的特殊处理
                return this.fillElementUIInput(element, value);
            } else {
                // 传统输入框处理
                return this.fillTraditionalInput(element, value);
            }
        }

        return false;
    }

    /**
     * 填充Element UI输入框
     */
    fillElementUIInput(element, value) {
        try {
            // 清空现有值
            element.value = '';
            element.focus();

            // 模拟用户输入 - Element UI需要特殊的事件处理
            element.value = value;

            // 触发Vue的响应式更新
            const inputEvent = new Event('input', { bubbles: true });
            const changeEvent = new Event('change', { bubbles: true });

            element.dispatchEvent(inputEvent);
            element.dispatchEvent(changeEvent);

            // 触发blur事件完成输入
            element.blur();

            console.log(`成功填充Element UI输入框: ${value}`);
            return true;
        } catch (error) {
            console.error('填充Element UI输入框失败:', error);
            return false;
        }
    }

    /**
     * 填充传统输入框
     */
    fillTraditionalInput(element, value) {
        try {
            // 清空现有值
            element.value = '';
            element.focus();

            // 模拟用户输入
            element.value = value;

            // 触发相关事件
            this.triggerEvents(element, ['input', 'change', 'blur']);

            console.log(`成功填充传统输入框: ${value}`);
            return true;
        } catch (error) {
            console.error('填充传统输入框失败:', error);
            return false;
        }
    }

    /**
     * 填充下拉框字段
     */
    async fillSelectField(key, fieldInfo) {
        if (!this.config.autoDropdown) {
            return false;
        }

        const { element, options } = fieldInfo;

        // 检查字段是否已有值
        if (this.hasValue(element)) {
            console.log(`下拉框字段 ${key} 已有值，跳过填充`);
            return false;
        }

        // 检查是否是Element UI选择框
        const isElementUISelect = element.closest('.el-select') !== null;

        if (isElementUISelect) {
            return await this.fillElementUISelect(element, key, fieldInfo);
        } else {
            return this.fillTraditionalSelect(element, options, key);
        }
    }

    /**
     * 填充Element UI选择框
     */
    async fillElementUISelect(element, key, fieldInfo) {
        return new Promise((resolve) => {
            try {
                // 检查字段是否已有值
                if (element.value && element.value.trim() !== '') {
                    console.log(`Element UI选择框 ${key} 已有值，跳过填充`);
                    resolve(false);
                    return;
                }

                // 获取首选值
                const preferredValue = this.getPreferredSelectValue(key);

                // 点击打开下拉框
                element.click();

                // 设置超时保护，避免无限等待
                const timeout = setTimeout(() => {
                    console.warn(`Element UI选择框 ${key} 填充超时`);
                    element.blur();
                    resolve(false);
                }, 2000);

                // 等待下拉选项加载
                setTimeout(() => {
                    try {
                        const dropdownOptions = document.querySelectorAll('.el-select-dropdown__item:not(.is-disabled)');

                        if (dropdownOptions.length > 0) {
                            let targetOption = null;

                            // 尝试找到首选值
                            if (preferredValue) {
                                for (const option of dropdownOptions) {
                                    if (option.textContent.includes(preferredValue)) {
                                        targetOption = option;
                                        break;
                                    }
                                }
                            }

                            // 如果没有找到首选值，选择第一个选项
                            if (!targetOption && dropdownOptions.length > 0) {
                                targetOption = dropdownOptions[0];
                            }

                            if (targetOption) {
                                targetOption.click();
                                console.log(`Element UI选择框 ${key} 选择了: ${targetOption.textContent}`);
                                clearTimeout(timeout);
                                resolve(true);
                                return;
                            }
                        }

                        // 如果没有找到选项，关闭下拉框
                        element.blur();
                        clearTimeout(timeout);
                        resolve(false);
                    } catch (error) {
                        console.error('处理下拉选项时出错:', error);
                        element.blur();
                        clearTimeout(timeout);
                        resolve(false);
                    }
                }, 300);

            } catch (error) {
                console.error('填充Element UI选择框失败:', error);
                resolve(false);
            }
        });
    }

    /**
     * 填充传统选择框
     */
    fillTraditionalSelect(element, options, key) {
        if (options && options.length > 0) {
            let selectedIndex = -1;

            // 尝试根据配置选择特定值
            const preferredValue = this.getPreferredSelectValue(key);
            if (preferredValue) {
                for (let i = 0; i < options.length; i++) {
                    if (options[i].text.includes(preferredValue) ||
                        options[i].value.includes(preferredValue)) {
                        selectedIndex = i;
                        break;
                    }
                }
            }

            // 如果没有找到首选值，选择第一个非空选项
            if (selectedIndex === -1) {
                for (let i = 0; i < options.length; i++) {
                    if (options[i].value && options[i].value.trim() !== '') {
                        selectedIndex = i;
                        break;
                    }
                }
            }

            if (selectedIndex !== -1) {
                element.selectedIndex = selectedIndex;
                this.triggerEvents(element, ['change']);
                console.log(`传统下拉框 ${key} 选择了: ${options[selectedIndex].text}`);
                return true;
            }
        }

        return false;
    }

    /**
     * 填充复选框/单选框字段
     */
    fillCheckboxField(key, fieldInfo) {
        if (!this.config.autoCheckbox) {
            return false;
        }
        
        const { element, type } = fieldInfo;
        
        // 默认选择"是"或第一个选项
        if (type === 'checkbox') {
            element.checked = true;
            this.triggerEvents(element, ['change']);
            return true;
        } else if (type === 'radio') {
            // 对于单选框，尝试选择值为"是"、"1"或"true"的选项
            const yesValues = ['是', '1', 'true', 'yes', 'Y'];
            if (yesValues.includes(element.value)) {
                element.checked = true;
                this.triggerEvents(element, ['change']);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 填充日期字段
     */
    fillDateField(key, fieldInfo) {
        if (!this.config.autoDate) {
            return false;
        }
        
        const { element } = fieldInfo;
        let date = new Date();
        
        // 根据字段类型应用日期偏移
        if (key.includes('delivery')) {
            date.setDate(date.getDate() + (this.config.dateOffsets?.delivery || 7));
        } else if (key.includes('rent') || key.includes('计租')) {
            date.setDate(date.getDate() + (this.config.dateOffsets?.rent || 14));
        } else if (key.includes('opening') || key.includes('开业')) {
            date.setDate(date.getDate() + (this.config.dateOffsets?.opening || 30));
        }
        
        const dateString = date.toISOString().split('T')[0];
        element.value = dateString;
        this.triggerEvents(element, ['input', 'change']);
        
        console.log(`日期字段 ${key} 设置为: ${dateString}`);
        return true;
    }

    /**
     * 推断文本字段的值
     */
    inferTextValue(field) {
        const name = (field.name || '').toLowerCase();
        const placeholder = (field.placeholder || '').toLowerCase();
        const id = (field.id || '').toLowerCase();
        
        // 根据字段属性推断合适的值
        if (name.includes('name') || name.includes('姓名')) {
            return '测试用户';
        }
        if (name.includes('phone') || name.includes('电话') || name.includes('手机')) {
            return '13800138000';
        }
        if (name.includes('email') || name.includes('邮箱')) {
            return '<EMAIL>';
        }
        if (name.includes('address') || name.includes('地址')) {
            return '测试地址';
        }
        if (name.includes('company') || name.includes('公司')) {
            return '测试公司';
        }
        
        return '测试内容';
    }

    /**
     * 触发DOM事件
     */
    triggerEvents(element, eventTypes) {
        eventTypes.forEach(eventType => {
            const event = new Event(eventType, {
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }

    /**
     * 获取下拉框首选值
     */
    getPreferredSelectValue(key) {
        // 根据字段类型返回首选值
        const preferences = {
            'dropdown_shopType': ['餐饮店', '零售店'],
            'dropdown_supplierType': ['一级供应商'],
            'dropdown_contractEntity': ['公司'],
            'dropdown_businessMode': ['直营', '加盟'],
            'dropdown_leaseType': ['长期租赁'],
            'dropdown_rentIncrease': ['不递增', '固定递增'],
            'dropdown_operationFeeIncrease': ['不递增'],
            'dropdown_propertyFeeIncrease': ['不递增']
        };
        
        const preferred = preferences[key];
        return preferred ? preferred[0] : null;
    }

    /**
     * 高亮显示字段
     */
    highlightField(element) {
        if (!element) return;
        
        // 保存原始样式
        element._originalStyle = {
            border: element.style.border,
            backgroundColor: element.style.backgroundColor,
            boxShadow: element.style.boxShadow
        };
        
        // 应用高亮样式
        element.style.border = '2px solid #4CAF50';
        element.style.backgroundColor = '#E8F5E8';
        element.style.boxShadow = '0 0 5px rgba(76, 175, 80, 0.5)';
        element.style.transition = 'all 0.3s ease';
    }

    /**
     * 移除字段高亮
     */
    removeHighlight(element) {
        if (!element || !element._originalStyle) return;
        
        // 恢复原始样式
        element.style.border = element._originalStyle.border;
        element.style.backgroundColor = element._originalStyle.backgroundColor;
        element.style.boxShadow = element._originalStyle.boxShadow;
        
        // 清理保存的样式
        delete element._originalStyle;
    }

    /**
     * 验证字段
     */
    validateFields() {
        const errors = [];
        const requiredFields = this.config?.validation?.requiredFields || ['brandScope'];
        const fieldTypes = this.config?.validation?.fieldTypes || {};
        
        // 检查必填字段
        for (const requiredField of requiredFields) {
            if (!this.fields.has(requiredField)) {
                errors.push({
                    field: requiredField,
                    type: 'missing',
                    message: `缺少必填字段: ${requiredField}`
                });
            }
        }
        
        // 检查字段类型格式
        for (const [fieldKey, fieldInfo] of this.fields) {
            const fieldType = this.getFieldTypeForValidation(fieldKey);
            if (fieldTypes[fieldType]) {
                const pattern = new RegExp(fieldTypes[fieldType]);
                const value = this.getFieldValue(fieldKey, fieldInfo);
                
                if (value && !pattern.test(value)) {
                    errors.push({
                        field: fieldKey,
                        type: 'format',
                        message: `字段格式不正确: ${fieldKey}`,
                        value: value
                    });
                }
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 获取字段类型用于验证
     */
    getFieldTypeForValidation(fieldKey) {
        if (fieldKey.includes('phone') || fieldKey.includes('电话')) return 'phone';
        if (fieldKey.includes('email') || fieldKey.includes('邮箱')) return 'email';
        if (fieldKey.includes('date') || fieldKey.includes('日期')) return 'date';
        return 'text';
    }

    /**
     * 获取字段值
     */
    getFieldValue(fieldKey, fieldInfo) {
        if (!fieldInfo || !fieldInfo.element) return null;
        
        const element = fieldInfo.element;
        switch (element.type) {
            case 'text':
            case 'email':
            case 'tel':
            case 'date':
                return element.value;
            case 'select-one':
                return element.selectedOptions[0]?.value;
            case 'checkbox':
            case 'radio':
                return element.checked;
            default:
                return element.value || element.textContent;
        }
    }

    /**
     * 安全的元素操作
     */
    safeElementOperation(element, operation, ...args) {
        try {
            if (!element || !element.isConnected) {
                throw new Error('元素不存在或已从DOM中移除');
            }
            
            if (element.disabled || element.readOnly) {
                throw new Error('元素被禁用或只读');
            }
            
            if (element.offsetParent === null && element.style.display !== 'none') {
                console.warn('元素可能不可见');
            }
            
            return operation.apply(element, args);
        } catch (error) {
            console.error('元素操作失败:', error);
            throw error;
        }
    }

    /**
     * 错误报告
     */
    reportError(context, error, additionalInfo = {}) {
        const errorReport = {
            timestamp: new Date().toISOString(),
            context,
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name
            },
            additionalInfo,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        console.error('错误报告:', errorReport);
        
        // 可以在这里添加错误上报逻辑
        // 例如发送到错误监控服务
        
        return errorReport;
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化表单自动填充器
const formAutoFiller = new FormAutoFiller();

// 页面加载完成后的额外初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM加载完成，表单自动填充器就绪');
    });
} else {
    console.log('表单自动填充器就绪');
}