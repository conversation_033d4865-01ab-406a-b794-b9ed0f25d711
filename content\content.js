/**
 * 表单自动填充插件 - 内容脚本
 * 负责在网页中识别和操作表单字段
 */

class FormAutoFiller {
    constructor() {
        this.fields = new Map();
        this.config = null;
        this.fieldSelectors = this.getFieldSelectors();
        
        this.init();
    }

    /**
     * 初始化内容脚本
     */
    init() {
        // 监听来自弹窗的消息
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // 保持消息通道开放
        });
        
        console.log('表单自动填充插件已加载');
    }

    /**
     * 处理来自弹窗的消息
     */
    async handleMessage(message, sender, sendResponse) {
        try {
            const { action, config } = message;
            this.config = config;
            
            switch (action) {
                case 'previewFields':
                    const fieldCount = await this.identifyFields();
                    sendResponse({ success: true, fieldCount });
                    break;
                    
                case 'fillForm':
                    const filledCount = await this.fillAllFields();
                    sendResponse({ success: true, filledCount });
                    break;
                    
                default:
                    sendResponse({ success: false, error: '未知操作' });
            }
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    /**
     * 获取字段选择器配置
     */
    getFieldSelectors() {
        return {
            // 签约品牌经营范围
            brandScope: [
                'input[name*="经营范围"]',
                'input[name*="brand"]',
                'input[name*="scope"]',
                'textarea[name*="经营范围"]',
                'input[placeholder*="经营范围"]',
                'input[id*="business-scope"]',
                'input[class*="scope"]',
                'input[aria-label*="经营范围"]'
            ],
            
            // 下拉框字段
            dropdowns: {
                shopType: [
                    'select[name*="商铺类型"]',
                    'select[name*="shop"]',
                    'select[name*="store"]'
                ],
                supplierType: [
                    'select[name*="供应商类型"]',
                    'select[name*="supplier"]'
                ],
                contractEntity: [
                    'select[name*="签约主体"]',
                    'select[name*="contract"]',
                    'select[name*="entity"]'
                ],
                businessMode: [
                    'select[name*="经营模式"]',
                    'select[name*="business"]',
                    'select[name*="mode"]'
                ],
                leaseType: [
                    'select[name*="租赁类型"]',
                    'select[name*="lease"]',
                    'select[name*="rent"]'
                ],
                rentIncrease: [
                    'select[name*="租金递增"]',
                    'select[name*="rent"][name*="increase"]'
                ],
                operationFeeIncrease: [
                    'select[name*="运营管理费"][name*="递增"]',
                    'select[name*="operation"][name*="fee"]'
                ],
                propertyFeeIncrease: [
                    'select[name*="物业管理费"][name*="递增"]',
                    'select[name*="property"][name*="fee"]'
                ]
            },
            
            // 复选框/单选框
            checkboxes: {
                renovation: [
                    'input[type="checkbox"][name*="装修"]',
                    'input[type="radio"][name*="装修"]',
                    'input[name*="renovation"]'
                ]
            },
            
            // 日期字段
            dates: {
                leaseStart: [
                    'input[type="date"][name*="租赁"][name*="开始"]',
                    'input[type="date"][name*="lease"][name*="start"]',
                    'input[name*="租赁起始"]'
                ],
                leaseEnd: [
                    'input[type="date"][name*="租赁"][name*="结束"]',
                    'input[type="date"][name*="lease"][name*="end"]',
                    'input[name*="租赁截止"]'
                ],
                deliveryDate: [
                    'input[type="date"][name*="交付"]',
                    'input[name*="delivery"]',
                    'input[name*="商户交付"]'
                ],
                rentStartDate: [
                    'input[type="date"][name*="计租"]',
                    'input[name*="rent"][name*="start"]',
                    'input[name*="商户计租"]'
                ],
                openingDate: [
                    'input[type="date"][name*="开业"]',
                    'input[name*="opening"]',
                    'input[name*="商户开业"]'
                ]
            }
        };
    }

    /**
     * 识别页面中的表单字段
     */
    async identifyFields() {
        this.fields.clear();
        let totalCount = 0;
        
        try {
            console.log('开始识别表单字段...');
            
            // 识别品牌经营范围字段
            const brandScopeField = this.findFieldBySelectors(this.fieldSelectors.brandScope);
            if (brandScopeField) {
                this.fields.set('brandScope', {
                    element: brandScopeField,
                    type: 'text',
                    label: this.getFieldLabel(brandScopeField)
                });
                totalCount++;
                console.log('识别到品牌经营范围字段:', brandScopeField);
            }
            
            // 识别下拉框字段
            for (const [key, selectors] of Object.entries(this.fieldSelectors.dropdowns)) {
                const field = this.findFieldBySelectors(selectors);
                if (field) {
                    this.fields.set(`dropdown_${key}`, {
                        element: field,
                        type: 'select',
                        label: this.getFieldLabel(field),
                        options: this.getSelectOptions(field)
                    });
                    totalCount++;
                    console.log(`识别到下拉框字段 ${key}:`, field);
                }
            }
            
            // 识别复选框字段
            for (const [key, selectors] of Object.entries(this.fieldSelectors.checkboxes)) {
                const field = this.findFieldBySelectors(selectors);
                if (field) {
                    this.fields.set(`checkbox_${key}`, {
                        element: field,
                        type: field.type,
                        label: this.getFieldLabel(field)
                    });
                    totalCount++;
                    console.log(`识别到复选框字段 ${key}:`, field);
                }
            }
            
            // 识别日期字段
            for (const [key, selectors] of Object.entries(this.fieldSelectors.dates)) {
                const field = this.findFieldBySelectors(selectors);
                if (field) {
                    this.fields.set(`date_${key}`, {
                        element: field,
                        type: 'date',
                        label: this.getFieldLabel(field)
                    });
                    totalCount++;
                    console.log(`识别到日期字段 ${key}:`, field);
                }
            }
            
            // 通用字段识别（作为补充）
            const genericFields = this.findGenericFields();
            genericFields.forEach((field, index) => {
                if (!this.isFieldAlreadyIdentified(field)) {
                    this.fields.set(`generic_${index}`, {
                        element: field,
                        type: this.getFieldType(field),
                        label: this.getFieldLabel(field)
                    });
                    totalCount++;
                }
            });
            
            console.log(`识别到 ${totalCount} 个表单字段:`, this.fields);
            return totalCount;
            
        } catch (error) {
            console.error('识别字段失败:', error);
            return 0;
        }
    }

    /**
     * 根据选择器查找字段
     */
    findFieldBySelectors(selectors) {
        for (const selector of selectors) {
            try {
                const element = document.querySelector(selector);
                if (element && this.isValidFormField(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`选择器 "${selector}" 无效:`, error);
            }
        }
        return null;
    }

    /**
     * 查找通用表单字段
     */
    findGenericFields() {
        const fields = [];
        const selectors = [
            'input[type="text"]',
            'input[type="email"]',
            'input[type="tel"]',
            'input[type="number"]',
            'input[type="date"]',
            'input[type="datetime-local"]',
            'select',
            'textarea',
            'input[type="checkbox"]',
            'input[type="radio"]'
        ];
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (this.isValidFormField(element)) {
                    fields.push(element);
                }
            });
        });
        
        return fields;
    }

    /**
     * 检查是否为有效的表单字段
     */
    isValidFormField(element) {
        // 检查元素是否可见
        if (!this.isElementVisible(element)) {
            return false;
        }
        
        // 检查是否为只读或禁用
        if (element.readOnly || element.disabled) {
            return false;
        }
        
        // 检查是否为隐藏字段
        if (element.type === 'hidden') {
            return false;
        }
        
        return true;
    }

    /**
     * 检查元素是否可见
     */
    isElementVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               style.opacity !== '0' &&
               element.offsetWidth > 0 && 
               element.offsetHeight > 0;
    }

    /**
     * 检查字段是否已被识别
     */
    isFieldAlreadyIdentified(element) {
        for (const fieldInfo of this.fields.values()) {
            if (fieldInfo.element === element) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(element) {
        // 尝试多种方式获取字段标签
        let label = '';
        
        // 1. 通过 aria-label 属性
        if (element.getAttribute('aria-label')) {
            label = element.getAttribute('aria-label');
        }
        // 2. 通过关联的 label 元素
        else if (element.id) {
            const labelElement = document.querySelector(`label[for="${element.id}"]`);
            if (labelElement) {
                label = labelElement.textContent.trim();
            }
        }
        // 3. 通过父级 label 元素
        else {
            const parentLabel = element.closest('label');
            if (parentLabel) {
                label = parentLabel.textContent.replace(element.value || '', '').trim();
            }
        }
        // 4. 通过 placeholder 属性
        if (!label && element.placeholder) {
            label = element.placeholder;
        }
        // 5. 通过 name 属性
        if (!label && element.name) {
            label = element.name;
        }
        
        return label || '未知字段';
    }

    /**
     * 获取下拉框选项
     */
    getSelectOptions(selectElement) {
        const options = [];
        if (selectElement.tagName.toLowerCase() === 'select') {
            for (let i = 0; i < selectElement.options.length; i++) {
                const option = selectElement.options[i];
                options.push({
                    value: option.value,
                    text: option.textContent.trim(),
                    index: i
                });
            }
        }
        return options;
    }

    /**
     * 填充所有识别到的字段
     */
    async fillAllFields() {
        let filledCount = 0;
        let errorCount = 0;
        const errors = [];
        const delay = this.config.fillDelay || 100;
        const maxRetries = this.config.maxRetries || 3;
        
        try {
            console.log('开始填充表单字段...');
            
            // 验证必填字段
            const validationResult = this.validateFields();
            if (!validationResult.isValid) {
                return {
                    success: false,
                    error: '字段验证失败',
                    details: validationResult.errors,
                    filledCount: 0
                };
            }
            
            for (const [key, fieldInfo] of this.fields) {
                let retryCount = 0;
                let fieldFilled = false;
                
                while (retryCount < maxRetries && !fieldFilled) {
                    try {
                        console.log(`正在填充字段: ${key} (尝试 ${retryCount + 1}/${maxRetries}): ${fieldInfo.label}`);
                        
                        const success = await this.fillField(key, fieldInfo);
                        if (success) {
                            filledCount++;
                            fieldFilled = true;
                            console.log(`成功填充字段 ${key}: ${fieldInfo.label}`);
                        }
                        
                    } catch (error) {
                        retryCount++;
                        console.error(`填充字段 ${key} 时出错 (尝试 ${retryCount}/${maxRetries}):`, error);
                        
                        if (retryCount >= maxRetries) {
                            errorCount++;
                            errors.push({
                                field: key,
                                error: error.message || '未知错误',
                                element: fieldInfo.element?.tagName || 'unknown'
                            });
                        } else {
                            // 重试前等待更长时间
                            await this.sleep(delay * 2);
                        }
                    }
                }
                
                // 填充间隔
                if (delay > 0 && fieldFilled) {
                    await this.sleep(delay);
                }
            }
            
            console.log(`填充完成，成功填充 ${filledCount} 个字段，失败 ${errorCount} 个字段`);
            
            return {
                success: errorCount === 0,
                filledCount,
                errorCount,
                errors: errors.length > 0 ? errors : undefined,
                message: errorCount > 0 ? `部分字段填充失败 (${errorCount}个)` : '所有字段填充成功'
            };
            
        } catch (error) {
            console.error('填充字段失败:', error);
            return {
                success: false,
                error: error.message,
                filledCount,
                errorCount
            };
        }
    }

    /**
     * 填充单个字段
     */
    async fillField(key, fieldInfo) {
        try {
            const { element, type } = fieldInfo;
            
            // 检查字段是否仍然有效
            if (!this.isValidFormField(element)) {
                console.warn(`字段 ${key} 不再有效，跳过填充`);
                return false;
            }
            
            // 高亮显示正在填充的字段
            if (this.config.highlightFields) {
                this.highlightField(element);
            }
            
            let success = false;
            
            switch (type) {
                case 'text':
                case 'textarea':
                    success = this.fillTextField(key, fieldInfo);
                    break;
                    
                case 'select':
                    success = this.fillSelectField(key, fieldInfo);
                    break;
                    
                case 'checkbox':
                case 'radio':
                    success = this.fillCheckboxField(key, fieldInfo);
                    break;
                    
                case 'date':
                    success = this.fillDateField(key, fieldInfo);
                    break;
                    
                default:
                    console.warn(`不支持的字段类型: ${type}`);
                    success = false;
            }
            
            // 移除高亮
            if (this.config.highlightFields) {
                setTimeout(() => this.removeHighlight(element), 1000);
            }
            
            return success;
            
        } catch (error) {
            console.error(`填充字段 ${key} 失败:`, error);
            return false;
        }
    }

    /**
     * 获取字段类型
     */
    getFieldType(field) {
        if (field.tagName.toLowerCase() === 'select') {
            return 'select';
        }
        if (field.tagName.toLowerCase() === 'textarea') {
            return 'textarea';
        }
        if (field.type === 'checkbox' || field.type === 'radio') {
            return field.type;
        }
        if (field.type === 'date' || field.type === 'datetime-local') {
            return 'date';
        }
        return 'text';
    }

    /**
     * 填充文本字段
     */
    fillTextField(key, fieldInfo) {
        const { element } = fieldInfo;
        let value = '';
        
        if (key === 'brandScope') {
            value = this.config.brandScope || '测试';
        } else {
            // 根据字段名称或属性推断合适的值
            value = this.inferTextValue(element);
        }
        
        if (value) {
            // 清空现有值
            element.value = '';
            element.focus();
            
            // 模拟用户输入
            element.value = value;
            
            // 触发相关事件
            this.triggerEvents(element, ['input', 'change', 'blur']);
            return true;
        }
        
        return false;
    }

    /**
     * 填充下拉框字段
     */
    fillSelectField(key, fieldInfo) {
        if (!this.config.autoDropdown) {
            return false;
        }
        
        const { element, options } = fieldInfo;
        
        if (options && options.length > 0) {
            let selectedIndex = -1;
            
            // 尝试根据配置选择特定值
            const preferredValue = this.getPreferredSelectValue(key);
            if (preferredValue) {
                for (let i = 0; i < options.length; i++) {
                    if (options[i].text.includes(preferredValue) || 
                        options[i].value.includes(preferredValue)) {
                        selectedIndex = i;
                        break;
                    }
                }
            }
            
            // 如果没有找到首选值，选择第一个非空选项
            if (selectedIndex === -1) {
                for (let i = 0; i < options.length; i++) {
                    if (options[i].value && options[i].value.trim() !== '') {
                        selectedIndex = i;
                        break;
                    }
                }
            }
            
            if (selectedIndex !== -1) {
                element.selectedIndex = selectedIndex;
                this.triggerEvents(element, ['change']);
                console.log(`下拉框 ${key} 选择了: ${options[selectedIndex].text}`);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 填充复选框/单选框字段
     */
    fillCheckboxField(key, fieldInfo) {
        if (!this.config.autoCheckbox) {
            return false;
        }
        
        const { element, type } = fieldInfo;
        
        // 默认选择"是"或第一个选项
        if (type === 'checkbox') {
            element.checked = true;
            this.triggerEvents(element, ['change']);
            return true;
        } else if (type === 'radio') {
            // 对于单选框，尝试选择值为"是"、"1"或"true"的选项
            const yesValues = ['是', '1', 'true', 'yes', 'Y'];
            if (yesValues.includes(element.value)) {
                element.checked = true;
                this.triggerEvents(element, ['change']);
                return true;
            }
        }
        
        return false;
    }

    /**
     * 填充日期字段
     */
    fillDateField(key, fieldInfo) {
        if (!this.config.autoDate) {
            return false;
        }
        
        const { element } = fieldInfo;
        let date = new Date();
        
        // 根据字段类型应用日期偏移
        if (key.includes('delivery')) {
            date.setDate(date.getDate() + (this.config.dateOffsets?.delivery || 7));
        } else if (key.includes('rent') || key.includes('计租')) {
            date.setDate(date.getDate() + (this.config.dateOffsets?.rent || 14));
        } else if (key.includes('opening') || key.includes('开业')) {
            date.setDate(date.getDate() + (this.config.dateOffsets?.opening || 30));
        }
        
        const dateString = date.toISOString().split('T')[0];
        element.value = dateString;
        this.triggerEvents(element, ['input', 'change']);
        
        console.log(`日期字段 ${key} 设置为: ${dateString}`);
        return true;
    }

    /**
     * 推断文本字段的值
     */
    inferTextValue(field) {
        const name = (field.name || '').toLowerCase();
        const placeholder = (field.placeholder || '').toLowerCase();
        const id = (field.id || '').toLowerCase();
        
        // 根据字段属性推断合适的值
        if (name.includes('name') || name.includes('姓名')) {
            return '测试用户';
        }
        if (name.includes('phone') || name.includes('电话') || name.includes('手机')) {
            return '13800138000';
        }
        if (name.includes('email') || name.includes('邮箱')) {
            return '<EMAIL>';
        }
        if (name.includes('address') || name.includes('地址')) {
            return '测试地址';
        }
        if (name.includes('company') || name.includes('公司')) {
            return '测试公司';
        }
        
        return '测试内容';
    }

    /**
     * 触发DOM事件
     */
    triggerEvents(element, eventTypes) {
        eventTypes.forEach(eventType => {
            const event = new Event(eventType, {
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });
    }

    /**
     * 获取下拉框首选值
     */
    getPreferredSelectValue(key) {
        // 根据字段类型返回首选值
        const preferences = {
            'dropdown_shopType': ['餐饮店', '零售店'],
            'dropdown_supplierType': ['一级供应商'],
            'dropdown_contractEntity': ['公司'],
            'dropdown_businessMode': ['直营', '加盟'],
            'dropdown_leaseType': ['长期租赁'],
            'dropdown_rentIncrease': ['不递增', '固定递增'],
            'dropdown_operationFeeIncrease': ['不递增'],
            'dropdown_propertyFeeIncrease': ['不递增']
        };
        
        const preferred = preferences[key];
        return preferred ? preferred[0] : null;
    }

    /**
     * 高亮显示字段
     */
    highlightField(element) {
        if (!element) return;
        
        // 保存原始样式
        element._originalStyle = {
            border: element.style.border,
            backgroundColor: element.style.backgroundColor,
            boxShadow: element.style.boxShadow
        };
        
        // 应用高亮样式
        element.style.border = '2px solid #4CAF50';
        element.style.backgroundColor = '#E8F5E8';
        element.style.boxShadow = '0 0 5px rgba(76, 175, 80, 0.5)';
        element.style.transition = 'all 0.3s ease';
    }

    /**
     * 移除字段高亮
     */
    removeHighlight(element) {
        if (!element || !element._originalStyle) return;
        
        // 恢复原始样式
        element.style.border = element._originalStyle.border;
        element.style.backgroundColor = element._originalStyle.backgroundColor;
        element.style.boxShadow = element._originalStyle.boxShadow;
        
        // 清理保存的样式
        delete element._originalStyle;
    }

    /**
     * 验证字段
     */
    validateFields() {
        const errors = [];
        const requiredFields = this.config?.validation?.requiredFields || ['brandScope'];
        const fieldTypes = this.config?.validation?.fieldTypes || {};
        
        // 检查必填字段
        for (const requiredField of requiredFields) {
            if (!this.fields.has(requiredField)) {
                errors.push({
                    field: requiredField,
                    type: 'missing',
                    message: `缺少必填字段: ${requiredField}`
                });
            }
        }
        
        // 检查字段类型格式
        for (const [fieldKey, fieldInfo] of this.fields) {
            const fieldType = this.getFieldTypeForValidation(fieldKey);
            if (fieldTypes[fieldType]) {
                const pattern = new RegExp(fieldTypes[fieldType]);
                const value = this.getFieldValue(fieldKey, fieldInfo);
                
                if (value && !pattern.test(value)) {
                    errors.push({
                        field: fieldKey,
                        type: 'format',
                        message: `字段格式不正确: ${fieldKey}`,
                        value: value
                    });
                }
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 获取字段类型用于验证
     */
    getFieldTypeForValidation(fieldKey) {
        if (fieldKey.includes('phone') || fieldKey.includes('电话')) return 'phone';
        if (fieldKey.includes('email') || fieldKey.includes('邮箱')) return 'email';
        if (fieldKey.includes('date') || fieldKey.includes('日期')) return 'date';
        return 'text';
    }

    /**
     * 获取字段值
     */
    getFieldValue(fieldKey, fieldInfo) {
        if (!fieldInfo || !fieldInfo.element) return null;
        
        const element = fieldInfo.element;
        switch (element.type) {
            case 'text':
            case 'email':
            case 'tel':
            case 'date':
                return element.value;
            case 'select-one':
                return element.selectedOptions[0]?.value;
            case 'checkbox':
            case 'radio':
                return element.checked;
            default:
                return element.value || element.textContent;
        }
    }

    /**
     * 安全的元素操作
     */
    safeElementOperation(element, operation, ...args) {
        try {
            if (!element || !element.isConnected) {
                throw new Error('元素不存在或已从DOM中移除');
            }
            
            if (element.disabled || element.readOnly) {
                throw new Error('元素被禁用或只读');
            }
            
            if (element.offsetParent === null && element.style.display !== 'none') {
                console.warn('元素可能不可见');
            }
            
            return operation.apply(element, args);
        } catch (error) {
            console.error('元素操作失败:', error);
            throw error;
        }
    }

    /**
     * 错误报告
     */
    reportError(context, error, additionalInfo = {}) {
        const errorReport = {
            timestamp: new Date().toISOString(),
            context,
            error: {
                message: error.message,
                stack: error.stack,
                name: error.name
            },
            additionalInfo,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        console.error('错误报告:', errorReport);
        
        // 可以在这里添加错误上报逻辑
        // 例如发送到错误监控服务
        
        return errorReport;
    }

    /**
     * 延迟函数
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 初始化表单自动填充器
const formAutoFiller = new FormAutoFiller();

// 页面加载完成后的额外初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        console.log('DOM加载完成，表单自动填充器就绪');
    });
} else {
    console.log('表单自动填充器就绪');
}