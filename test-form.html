<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        select {
            height: 40px;
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        .submit-btn {
            background-color: #007cba;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
        }
        .submit-btn:hover {
            background-color: #005a87;
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #007cba;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h1>🧪 表单自动填充测试页面</h1>
        
        <div class="instructions">
            <strong>测试说明：</strong>
            <ol>
                <li>安装并启用表单自动填充插件</li>
                <li>点击浏览器工具栏中的插件图标</li>
                <li>在弹出窗口中点击"预览识别"查看识别到的字段</li>
                <li>点击"一键填充"自动填写表单</li>
            </ol>
        </div>

        <form id="test-form">
            <!-- 品牌经营范围 -->
            <div class="form-group">
                <label for="brand-scope">品牌经营范围:</label>
                <input type="text" id="brand-scope" name="brandScope" placeholder="请输入经营范围">
            </div>

            <!-- 商铺类型 -->
            <div class="form-group">
                <label for="shop-type">商铺类型:</label>
                <select id="shop-type" name="shopType">
                    <option value="">请选择</option>
                    <option value="restaurant">餐饮</option>
                    <option value="retail">零售</option>
                    <option value="service">服务</option>
                </select>
            </div>

            <!-- 供应商类型 -->
            <div class="form-group">
                <label for="supplier-type">供应商类型:</label>
                <select id="supplier-type" name="supplierType">
                    <option value="">请选择</option>
                    <option value="direct">直营</option>
                    <option value="franchise">加盟</option>
                    <option value="agent">代理</option>
                </select>
            </div>

            <!-- 是否重新装修 -->
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="renovation" name="renovation" value="1">
                    <label for="renovation">是否重新装修</label>
                </div>
            </div>

            <!-- 商户交付日期 -->
            <div class="form-group">
                <label for="delivery-date">商户交付日期:</label>
                <input type="date" id="delivery-date" name="deliveryDate">
            </div>

            <!-- 商户计租日期 -->
            <div class="form-group">
                <label for="rent-start-date">商户计租日期:</label>
                <input type="date" id="rent-start-date" name="rentStartDate">
            </div>

            <!-- 商户开业日期 -->
            <div class="form-group">
                <label for="opening-date">商户开业日期:</label>
                <input type="date" id="opening-date" name="openingDate">
            </div>

            <!-- 租赁起止时间 -->
            <div class="form-group">
                <label for="lease-start">租赁开始时间:</label>
                <input type="date" id="lease-start" name="leaseStart">
            </div>

            <div class="form-group">
                <label for="lease-end">租赁结束时间:</label>
                <input type="date" id="lease-end" name="leaseEnd">
            </div>

            <!-- 经营模式 -->
            <div class="form-group">
                <label for="business-mode">经营模式:</label>
                <select id="business-mode" name="businessMode">
                    <option value="">请选择</option>
                    <option value="self">自营</option>
                    <option value="joint">联营</option>
                    <option value="lease">租赁</option>
                </select>
            </div>

            <!-- 备注 -->
            <div class="form-group">
                <label for="remarks">备注:</label>
                <textarea id="remarks" name="remarks" rows="3" placeholder="请输入备注信息"></textarea>
            </div>

            <button type="submit" class="submit-btn">提交表单</button>
        </form>
    </div>

    <script>
        // 表单提交处理
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 收集表单数据
            const formData = new FormData(this);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            // 显示结果
            alert('表单数据:\n' + JSON.stringify(data, null, 2));
            console.log('表单提交数据:', data);
        });

        // 页面加载完成提示
        window.addEventListener('load', function() {
            console.log('测试页面加载完成，可以开始测试插件功能');
        });
    </script>
</body>
</html>
