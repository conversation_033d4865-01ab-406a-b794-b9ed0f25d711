// 最简单的测试脚本
console.log('=== 插件最简测试开始 ===');

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM加载完成');
    
    // 更新状态
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = '已就绪';
    }
    
    // 添加按钮点击事件
    const testButton = document.getElementById('test-button');
    if (testButton) {
        testButton.addEventListener('click', function() {
            alert('插件正常工作！');
            console.log('测试按钮被点击');
        });
    }
});

// 立即执行测试
setTimeout(function() {
    console.log('延迟测试执行');
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = '测试完成';
    }
}, 1000);

console.log('=== 脚本加载完成 ===');