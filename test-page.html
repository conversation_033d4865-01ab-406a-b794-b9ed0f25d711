<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表单填充测试页面</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <style>
        .container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
        }
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <h1>表单填充测试页面</h1>
            
            <!-- Element UI 表单 -->
            <div class="form-section">
                <div class="section-title">Element UI 表单</div>
                <el-form :model="form" label-width="120px">
                    <el-form-item label="选择模板:">
                        <el-select v-model="form.template" placeholder="请选择模板">
                            <el-option label="默认模板" value="default"></el-option>
                            <el-option label="高级模板" value="advanced"></el-option>
                            <el-option label="自定义模板" value="custom"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="品牌经营范围:">
                        <el-input v-model="form.brandScope" placeholder="请输入品牌经营范围"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="商铺类型:">
                        <el-select v-model="form.shopType" placeholder="请选择商铺类型">
                            <el-option label="公司" value="company"></el-option>
                            <el-option label="个人" value="individual"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="商户交付日期:">
                        <el-date-picker v-model="form.deliveryDate" type="date" placeholder="选择日期"></el-date-picker>
                    </el-form-item>
                    
                    <el-form-item label="商户计租日期:">
                        <el-date-picker v-model="form.rentStartDate" type="date" placeholder="选择日期"></el-date-picker>
                    </el-form-item>
                    
                    <el-form-item label="自动填充选项:">
                        <el-checkbox v-model="form.autoDropdown">自动下拉</el-checkbox>
                        <el-checkbox v-model="form.autoDate">自动日期</el-checkbox>
                        <el-checkbox v-model="form.autoCheckbox">自动复选框</el-checkbox>
                    </el-form-item>
                </el-form>
            </div>
            
            <!-- 传统 HTML 表单 -->
            <div class="form-section">
                <div class="section-title">传统 HTML 表单</div>
                <form>
                    <div style="margin-bottom: 15px;">
                        <label for="traditional-brand">品牌经营范围:</label>
                        <input type="text" id="traditional-brand" name="brandScope" placeholder="请输入品牌经营范围" style="width: 300px; margin-left: 10px;">
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="traditional-shop">商铺类型:</label>
                        <select id="traditional-shop" name="shopType" style="width: 200px; margin-left: 10px;">
                            <option value="">请选择</option>
                            <option value="company">公司</option>
                            <option value="individual">个人</option>
                        </select>
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label for="traditional-date">交付日期:</label>
                        <input type="date" id="traditional-date" name="deliveryDate" style="margin-left: 10px;">
                    </div>
                    
                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" name="renovation" value="yes"> 需要装修
                        </label>
                    </div>
                </form>
            </div>
            
            <!-- 混合表单 -->
            <div class="form-section">
                <div class="section-title">混合表单</div>
                <div style="display: flex; gap: 20px;">
                    <div>
                        <label>邮箱:</label>
                        <input type="email" placeholder="请输入邮箱" style="width: 200px;">
                    </div>
                    <div>
                        <label>电话:</label>
                        <input type="tel" placeholder="请输入电话" style="width: 200px;">
                    </div>
                </div>
                <div style="margin-top: 15px;">
                    <label>备注:</label>
                    <textarea placeholder="请输入备注" style="width: 400px; height: 80px;"></textarea>
                </div>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data() {
                return {
                    form: {
                        template: '',
                        brandScope: '',
                        shopType: '',
                        deliveryDate: '',
                        rentStartDate: '',
                        autoDropdown: false,
                        autoDate: false,
                        autoCheckbox: false
                    }
                }
            },
            methods: {
                onSubmit() {
                    console.log('表单提交:', this.form);
                }
            }
        });
    </script>
</body>
</html>
