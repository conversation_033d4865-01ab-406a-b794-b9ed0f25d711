<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="48" height="48" rx="8" fill="url(#gradient1)"/>
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 表单线条 -->
  <rect x="8" y="12" width="32" height="3" rx="1.5" fill="white" opacity="0.9"/>
  <rect x="8" y="18" width="28" height="3" rx="1.5" fill="white" opacity="0.8"/>
  <rect x="8" y="24" width="30" height="3" rx="1.5" fill="white" opacity="0.7"/>
  <rect x="8" y="30" width="24" height="3" rx="1.5" fill="white" opacity="0.6"/>
  
  <!-- 自动填充图标 -->
  <circle cx="36" cy="32" r="8" fill="#667eea"/>
  <path d="M32 32l3 3 6-6" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- 装饰性元素 -->
  <circle cx="12" cy="38" r="2" fill="white" opacity="0.5"/>
  <circle cx="18" cy="40" r="1.5" fill="white" opacity="0.4"/>
  <circle cx="24" cy="38" r="1" fill="white" opacity="0.3"/>
</svg>